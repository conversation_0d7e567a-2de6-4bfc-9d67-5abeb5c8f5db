#!/usr/bin/env python3
"""
Recipe-Review Distribution Gap Analysis

This script analyzes the current recipe-review distribution to identify gaps
and create an optimization strategy for the recommendation engine.
"""

import os
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from bson import ObjectId
import json
from collections import defaultdict, Counter

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def analyze_recipe_review_distribution(db):
    """Analyze current recipe-review distribution."""
    print("\n📊 Analyzing Recipe-Review Distribution")
    print("=" * 50)
    
    recipes_collection = db['recipes']
    reviews_collection = db['recipe_reviews']
    
    # Get all recipes
    recipes = list(recipes_collection.find({}, {
        '_id': 1, 'name': 1, 'cuisine': 1, 'difficulty': 1, 
        'is_user_submitted': 1, 'created_at': 1
    }))
    
    print(f"📈 Total recipes: {len(recipes)}")
    
    # Count reviews per recipe
    review_counts = defaultdict(int)
    recipe_ratings = defaultdict(list)
    
    for review in reviews_collection.find():
        recipe_id = review.get('recipe_id')
        if recipe_id:
            review_counts[recipe_id] += 1
            rating = review.get('rating', 0)
            recipe_ratings[recipe_id].append(rating)
    
    # Categorize recipes by review count
    no_reviews = []
    few_reviews = []  # 1-2 reviews
    adequate_reviews = []  # 3-5 reviews
    many_reviews = []  # 6+ reviews
    
    cuisine_distribution = defaultdict(lambda: {'total': 0, 'no_reviews': 0, 'few_reviews': 0})
    difficulty_distribution = defaultdict(lambda: {'total': 0, 'no_reviews': 0, 'few_reviews': 0})
    
    for recipe in recipes:
        recipe_id = str(recipe['_id'])
        review_count = review_counts[recipe_id]
        cuisine = recipe.get('cuisine', 'Unknown')
        difficulty = recipe.get('difficulty', 'Unknown')
        
        # Update distributions
        cuisine_distribution[cuisine]['total'] += 1
        difficulty_distribution[difficulty]['total'] += 1
        
        if review_count == 0:
            no_reviews.append(recipe)
            cuisine_distribution[cuisine]['no_reviews'] += 1
            difficulty_distribution[difficulty]['no_reviews'] += 1
        elif review_count <= 2:
            few_reviews.append(recipe)
            cuisine_distribution[cuisine]['few_reviews'] += 1
            difficulty_distribution[difficulty]['few_reviews'] += 1
        elif review_count <= 5:
            adequate_reviews.append(recipe)
        else:
            many_reviews.append(recipe)
    
    # Print distribution analysis
    print(f"\n📊 Review Distribution Analysis:")
    print(f"  No reviews (0): {len(no_reviews)} recipes ({len(no_reviews)/len(recipes)*100:.1f}%)")
    print(f"  Few reviews (1-2): {len(few_reviews)} recipes ({len(few_reviews)/len(recipes)*100:.1f}%)")
    print(f"  Adequate reviews (3-5): {len(adequate_reviews)} recipes ({len(adequate_reviews)/len(recipes)*100:.1f}%)")
    print(f"  Many reviews (6+): {len(many_reviews)} recipes ({len(many_reviews)/len(recipes)*100:.1f}%)")
    
    # Recipes needing attention (0-2 reviews)
    needs_attention = no_reviews + few_reviews
    print(f"\n🎯 Recipes needing attention: {len(needs_attention)} ({len(needs_attention)/len(recipes)*100:.1f}%)")
    
    return {
        'total_recipes': len(recipes),
        'no_reviews': no_reviews,
        'few_reviews': few_reviews,
        'adequate_reviews': adequate_reviews,
        'many_reviews': many_reviews,
        'needs_attention': needs_attention,
        'cuisine_distribution': dict(cuisine_distribution),
        'difficulty_distribution': dict(difficulty_distribution),
        'review_counts': dict(review_counts),
        'recipe_ratings': dict(recipe_ratings)
    }

def analyze_cuisine_gaps(distribution_data):
    """Analyze gaps by cuisine type."""
    print(f"\n🍽️ Cuisine-Based Gap Analysis:")
    print("-" * 40)
    
    cuisine_dist = distribution_data['cuisine_distribution']
    priority_cuisines = []
    
    for cuisine, data in sorted(cuisine_dist.items(), key=lambda x: x[1]['total'], reverse=True):
        total = data['total']
        no_reviews = data['no_reviews']
        few_reviews = data['few_reviews']
        needs_attention = no_reviews + few_reviews
        percentage_needing_attention = (needs_attention / total * 100) if total > 0 else 0
        
        print(f"  {cuisine}: {total} recipes")
        print(f"    - No reviews: {no_reviews} ({no_reviews/total*100:.1f}%)")
        print(f"    - Few reviews: {few_reviews} ({few_reviews/total*100:.1f}%)")
        print(f"    - Needs attention: {needs_attention} ({percentage_needing_attention:.1f}%)")
        
        # Prioritize cuisines with many recipes but poor review coverage
        if total >= 10 and percentage_needing_attention > 50:
            priority_cuisines.append({
                'cuisine': cuisine,
                'total_recipes': total,
                'needs_attention': needs_attention,
                'priority_score': total * (percentage_needing_attention / 100)
            })
        print()
    
    return sorted(priority_cuisines, key=lambda x: x['priority_score'], reverse=True)

def analyze_user_review_patterns(db):
    """Analyze current user review patterns."""
    print(f"\n👥 User Review Pattern Analysis:")
    print("-" * 40)
    
    users_collection = db['users']
    reviews_collection = db['recipe_reviews']
    
    # Get user review counts
    user_review_counts = defaultdict(int)
    user_cuisines_reviewed = defaultdict(set)
    user_ratings = defaultdict(list)
    
    for review in reviews_collection.find():
        user_id = review.get('user_id')
        if user_id:
            user_review_counts[user_id] += 1
            user_ratings[user_id].append(review.get('rating', 0))
            
            # Get recipe cuisine
            recipe_id = review.get('recipe_id')
            if recipe_id:
                try:
                    # Handle both ObjectId and string formats
                    if isinstance(recipe_id, str) and len(recipe_id) == 24:
                        recipe_obj_id = ObjectId(recipe_id)
                    elif isinstance(recipe_id, ObjectId):
                        recipe_obj_id = recipe_id
                    else:
                        continue  # Skip invalid recipe_id

                    recipe = db['recipes'].find_one({'_id': recipe_obj_id})
                    if recipe:
                        cuisine = recipe.get('cuisine', 'Unknown')
                        user_cuisines_reviewed[user_id].add(cuisine)
                except:
                    continue  # Skip invalid ObjectId
    
    # Analyze patterns
    review_count_distribution = Counter(user_review_counts.values())
    
    print(f"📊 User Review Count Distribution:")
    for count, users in sorted(review_count_distribution.items()):
        print(f"  {count} reviews: {users} users")
    
    # Find bridge users (review multiple cuisines)
    bridge_users = []
    for user_id, cuisines in user_cuisines_reviewed.items():
        if len(cuisines) >= 3:  # Users who review 3+ different cuisines
            bridge_users.append({
                'user_id': user_id,
                'cuisines_count': len(cuisines),
                'review_count': user_review_counts[user_id],
                'cuisines': list(cuisines)
            })
    
    print(f"\n🌉 Bridge Users (review 3+ cuisines): {len(bridge_users)}")
    
    return {
        'user_review_counts': dict(user_review_counts),
        'user_cuisines_reviewed': {k: list(v) for k, v in user_cuisines_reviewed.items()},
        'bridge_users': bridge_users,
        'review_count_distribution': dict(review_count_distribution)
    }

def calculate_optimization_targets(distribution_data):
    """Calculate optimization targets for review generation."""
    print(f"\n🎯 Optimization Target Calculation:")
    print("-" * 40)
    
    total_recipes = distribution_data['total_recipes']
    needs_attention = len(distribution_data['needs_attention'])
    
    # Target: 3-5 reviews per recipe minimum
    min_target = 3
    max_target = 5
    
    # Calculate reviews needed
    current_total_reviews = sum(distribution_data['review_counts'].values())
    
    # For recipes with 0 reviews: need 3-4 reviews each
    no_reviews_count = len(distribution_data['no_reviews'])
    reviews_for_zero = no_reviews_count * 3.5  # Average 3.5 reviews
    
    # For recipes with 1-2 reviews: need to reach 3-4 reviews each
    few_reviews_needed = 0
    for recipe in distribution_data['few_reviews']:
        recipe_id = str(recipe['_id'])
        current_count = distribution_data['review_counts'].get(recipe_id, 0)
        needed = max(0, 3 - current_count)
        few_reviews_needed += needed
    
    total_reviews_needed = reviews_for_zero + few_reviews_needed
    target_total_reviews = current_total_reviews + total_reviews_needed
    
    print(f"📈 Current state:")
    print(f"  Total recipes: {total_recipes}")
    print(f"  Current total reviews: {current_total_reviews}")
    print(f"  Average reviews per recipe: {current_total_reviews/total_recipes:.2f}")
    
    print(f"\n🎯 Optimization targets:")
    print(f"  Recipes needing attention: {needs_attention}")
    print(f"  Reviews needed for 0-review recipes: {reviews_for_zero:.0f}")
    print(f"  Reviews needed for 1-2 review recipes: {few_reviews_needed}")
    print(f"  Total new reviews needed: {total_reviews_needed:.0f}")
    print(f"  Target total reviews: {target_total_reviews:.0f}")
    print(f"  Target average per recipe: {target_total_reviews/total_recipes:.2f}")
    
    return {
        'current_total_reviews': current_total_reviews,
        'total_reviews_needed': int(total_reviews_needed),
        'target_total_reviews': int(target_total_reviews),
        'recipes_needing_attention': needs_attention,
        'no_reviews_count': no_reviews_count,
        'few_reviews_count': len(distribution_data['few_reviews'])
    }

def generate_optimization_strategy(distribution_data, user_patterns, targets):
    """Generate comprehensive optimization strategy."""
    print(f"\n📋 Optimization Strategy:")
    print("=" * 50)
    
    strategy = {
        'phase_1_priority_recipes': [],
        'phase_2_standard_recipes': [],
        'user_expansion_needed': 0,
        'bridge_users_to_create': 0,
        'cuisine_priorities': []
    }
    
    # Phase 1: High priority recipes (user-submitted, popular cuisines)
    for recipe in distribution_data['needs_attention']:
        recipe_id = str(recipe['_id'])
        current_reviews = distribution_data['review_counts'].get(recipe_id, 0)
        
        is_high_priority = (
            recipe.get('is_user_submitted', False) or
            recipe.get('cuisine') in ['Malay', 'Chinese', 'Indian', 'Peranakan']
        )
        
        if is_high_priority:
            strategy['phase_1_priority_recipes'].append({
                'recipe_id': recipe_id,
                'name': recipe.get('name', 'Unknown'),
                'cuisine': recipe.get('cuisine', 'Unknown'),
                'current_reviews': current_reviews,
                'target_reviews': 4,
                'reviews_needed': max(0, 4 - current_reviews)
            })
        else:
            strategy['phase_2_standard_recipes'].append({
                'recipe_id': recipe_id,
                'name': recipe.get('name', 'Unknown'),
                'cuisine': recipe.get('cuisine', 'Unknown'),
                'current_reviews': current_reviews,
                'target_reviews': 3,
                'reviews_needed': max(0, 3 - current_reviews)
            })
    
    print(f"🚀 Phase 1 (Priority): {len(strategy['phase_1_priority_recipes'])} recipes")
    print(f"📝 Phase 2 (Standard): {len(strategy['phase_2_standard_recipes'])} recipes")
    
    # Calculate if we need more bridge users
    current_bridge_users = len(user_patterns['bridge_users'])
    total_users = len(user_patterns['user_review_counts'])
    bridge_ratio = current_bridge_users / total_users if total_users > 0 else 0
    
    target_bridge_ratio = 0.3  # 30% of users should be bridge users
    if bridge_ratio < target_bridge_ratio:
        strategy['bridge_users_to_create'] = int((target_bridge_ratio * total_users) - current_bridge_users)
    
    print(f"🌉 Bridge users needed: {strategy['bridge_users_to_create']}")
    
    return strategy

def save_analysis_results(distribution_data, user_patterns, targets, strategy):
    """Save analysis results to JSON file."""
    results = {
        'analysis_date': datetime.now().isoformat(),
        'distribution_summary': {
            'total_recipes': distribution_data['total_recipes'],
            'no_reviews': len(distribution_data['no_reviews']),
            'few_reviews': len(distribution_data['few_reviews']),
            'adequate_reviews': len(distribution_data['adequate_reviews']),
            'many_reviews': len(distribution_data['many_reviews'])
        },
        'targets': targets,
        'strategy': strategy,
        'cuisine_distribution': distribution_data['cuisine_distribution'],
        'user_patterns_summary': {
            'total_users': len(user_patterns['user_review_counts']),
            'bridge_users': len(user_patterns['bridge_users']),
            'review_count_distribution': user_patterns['review_count_distribution']
        }
    }
    
    with open('recipe_review_gap_analysis.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Analysis results saved to: recipe_review_gap_analysis.json")

def main():
    """Main analysis function."""
    print("📊 SisaRasa Recipe-Review Gap Analysis")
    print("=" * 50)
    print(f"Analysis started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Analyze recipe-review distribution
        distribution_data = analyze_recipe_review_distribution(db)
        
        # Analyze cuisine gaps
        priority_cuisines = analyze_cuisine_gaps(distribution_data)
        
        # Analyze user patterns
        user_patterns = analyze_user_review_patterns(db)
        
        # Calculate optimization targets
        targets = calculate_optimization_targets(distribution_data)
        
        # Generate strategy
        strategy = generate_optimization_strategy(distribution_data, user_patterns, targets)
        
        # Save results
        save_analysis_results(distribution_data, user_patterns, targets, strategy)
        
        print(f"\n✅ Analysis completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
