#!/usr/bin/env python3
"""
Validation script for synthetic data quality in SisaRasa.
This script checks the generated data and provides quality metrics.
"""

import pymongo
from collections import defaultdict, Counter
from datetime import datetime, timedelta

# Configuration
MONGO_URI = 'mongodb://localhost:27017/'
DATABASE_NAME = 'sisarasa'

class DataValidator:
    def __init__(self):
        self.client = pymongo.MongoClient(MONGO_URI)
        self.db = self.client[DATABASE_NAME]
    
    def validate_users(self):
        """Validate user data quality."""
        print("👥 Validating Users...")
        
        users = list(self.db.users.find({}))
        
        if not users:
            print("❌ No users found!")
            return False
        
        print(f"✅ Found {len(users)} users")
        
        # Check persona distribution
        personas = Counter(user.get('persona', 'unknown') for user in users)
        print("📊 Persona Distribution:")
        for persona, count in personas.items():
            print(f"   {persona}: {count}")
        
        # Check data completeness
        complete_users = 0
        for user in users:
            if (user.get('name') and user.get('email') and 
                user.get('dashboard_data') and user.get('analytics')):
                complete_users += 1
        
        print(f"✅ Complete user profiles: {complete_users}/{len(users)}")
        
        # Check search history
        users_with_searches = sum(1 for user in users 
                                if user.get('dashboard_data', {}).get('search_stats', {}).get('total_searches', 0) > 0)
        print(f"✅ Users with search history: {users_with_searches}/{len(users)}")
        
        return True
    
    def validate_reviews(self):
        """Validate review data quality."""
        print("\n⭐ Validating Reviews...")
        
        reviews = list(self.db.recipe_reviews.find({}))
        
        if not reviews:
            print("❌ No reviews found!")
            return False
        
        print(f"✅ Found {len(reviews)} reviews")
        
        # Check rating distribution
        ratings = Counter(review['rating'] for review in reviews)
        print("📊 Rating Distribution:")
        for rating in sorted(ratings.keys()):
            print(f"   {rating} stars: {ratings[rating]} ({ratings[rating]/len(reviews)*100:.1f}%)")
        
        # Check review text quality
        reviews_with_text = sum(1 for review in reviews if review.get('review_text'))
        print(f"✅ Reviews with text: {reviews_with_text}/{len(reviews)}")
        
        # Check vote distribution
        total_helpful = sum(review.get('helpful_votes', 0) for review in reviews)
        total_unhelpful = sum(review.get('unhelpful_votes', 0) for review in reviews)
        print(f"✅ Total helpful votes: {total_helpful}")
        print(f"✅ Total unhelpful votes: {total_unhelpful}")
        
        return True
    
    def validate_recipes(self):
        """Validate recipe data."""
        print("\n🍳 Validating Recipes...")
        
        recipes = list(self.db.recipes.find({}))
        
        if not recipes:
            print("❌ No recipes found in database!")
            return False
        
        print(f"✅ Found {len(recipes)} recipes in database")
        
        # Check recipe completeness
        complete_recipes = 0
        for recipe in recipes:
            if (recipe.get('name') and recipe.get('ingredients') and 
                recipe.get('original_id')):
                complete_recipes += 1
        
        print(f"✅ Complete recipes: {complete_recipes}/{len(recipes)}")
        
        return True
    
    def validate_search_patterns(self):
        """Validate search pattern realism."""
        print("\n🔍 Validating Search Patterns...")
        
        users = list(self.db.users.find({}))
        
        total_searches = 0
        ingredient_frequency = defaultdict(int)
        
        for user in users:
            dashboard_data = user.get('dashboard_data', {})
            search_stats = dashboard_data.get('search_stats', {})
            most_used = search_stats.get('most_used_ingredients', {})
            
            user_searches = search_stats.get('total_searches', 0)
            total_searches += user_searches
            
            for ingredient, count in most_used.items():
                ingredient_frequency[ingredient] += count
        
        print(f"✅ Total searches across all users: {total_searches}")
        
        # Show most popular ingredients
        if ingredient_frequency:
            print("📊 Most Popular Ingredients:")
            top_ingredients = sorted(ingredient_frequency.items(), 
                                   key=lambda x: x[1], reverse=True)[:10]
            for ingredient, count in top_ingredients:
                print(f"   {ingredient}: {count}")
        
        return True
    
    def validate_community_engagement(self):
        """Validate community interaction data."""
        print("\n👍 Validating Community Engagement...")
        
        votes = list(self.db.review_votes.find({}))
        
        if not votes:
            print("⚠️  No review votes found")
            return True
        
        print(f"✅ Found {len(votes)} review votes")
        
        helpful_votes = sum(1 for vote in votes if vote.get('is_helpful'))
        unhelpful_votes = len(votes) - helpful_votes
        
        print(f"✅ Helpful votes: {helpful_votes}")
        print(f"✅ Unhelpful votes: {unhelpful_votes}")
        
        return True
    
    def validate_analytics_data(self):
        """Validate user analytics data."""
        print("\n📈 Validating Analytics Data...")
        
        users = list(self.db.users.find({}))
        
        users_with_analytics = 0
        total_recipe_views = 0
        total_saves = 0
        total_reviews = 0
        
        for user in users:
            analytics = user.get('analytics', {})
            if analytics:
                users_with_analytics += 1
                total_recipe_views += analytics.get('total_recipe_views', 0)
                total_saves += analytics.get('total_recipe_saves', 0)
                total_reviews += analytics.get('total_reviews_given', 0)
        
        print(f"✅ Users with analytics: {users_with_analytics}/{len(users)}")
        print(f"✅ Total recipe views: {total_recipe_views}")
        print(f"✅ Total recipe saves: {total_saves}")
        print(f"✅ Total reviews given: {total_reviews}")
        
        return True
    
    def generate_summary_report(self):
        """Generate a comprehensive summary report."""
        print("\n" + "=" * 60)
        print("📋 SYNTHETIC DATA QUALITY REPORT")
        print("=" * 60)
        
        # Count all collections
        user_count = self.db.users.count_documents({})
        review_count = self.db.recipe_reviews.count_documents({})
        recipe_count = self.db.recipes.count_documents({})
        vote_count = self.db.review_votes.count_documents({})
        
        print(f"👥 Total Users: {user_count}")
        print(f"⭐ Total Reviews: {review_count}")
        print(f"🍳 Total Recipes: {recipe_count}")
        print(f"👍 Total Votes: {vote_count}")
        
        # Calculate ratios
        if user_count > 0:
            reviews_per_user = review_count / user_count
            print(f"📊 Average Reviews per User: {reviews_per_user:.1f}")
        
        if review_count > 0:
            votes_per_review = vote_count / review_count
            print(f"📊 Average Votes per Review: {votes_per_review:.1f}")
        
        # Check data freshness
        latest_user = self.db.users.find_one({}, sort=[('created_at', -1)])
        if latest_user:
            latest_date = latest_user['created_at']
            days_ago = (datetime.utcnow() - latest_date).days
            print(f"📅 Latest user created: {days_ago} days ago")
        
        print("\n🎯 Recommendation System Readiness:")
        print(f"   ✅ KNN Algorithm: {'Ready' if user_count > 10 else 'Needs more users'}")
        print(f"   ✅ Collaborative Filtering: {'Ready' if review_count > 50 else 'Needs more reviews'}")
        print(f"   ✅ Content-Based Filtering: {'Ready' if user_count > 10 else 'Needs more users'}")
        print(f"   ✅ Community Features: {'Ready' if vote_count > 10 else 'Needs more interactions'}")
        
        return True
    
    def run_full_validation(self):
        """Run complete validation suite."""
        print("🔍 SisaRasa Synthetic Data Validation")
        print("=" * 50)
        
        try:
            success = True
            success &= self.validate_users()
            success &= self.validate_reviews()
            success &= self.validate_recipes()
            success &= self.validate_search_patterns()
            success &= self.validate_community_engagement()
            success &= self.validate_analytics_data()
            
            self.generate_summary_report()
            
            if success:
                print("\n🎉 Validation Complete - Data Quality: EXCELLENT")
                print("Your SisaRasa system is ready for testing!")
            else:
                print("\n⚠️  Validation Complete - Some issues found")
                print("Consider regenerating data or checking MongoDB connection.")
            
        except Exception as e:
            print(f"\n❌ Validation failed: {e}")
        
        finally:
            self.client.close()

def main():
    """Main function."""
    validator = DataValidator()
    validator.run_full_validation()

if __name__ == "__main__":
    main()
