#!/usr/bin/env python3
"""
Quick test to run the Flask app and check if it works.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.getcwd())

try:
    from src.api.app import app, initialize_recommender
    
    print("✅ Successfully imported Flask app")
    
    # Initialize the recommender with a small dataset for testing
    print("🔄 Initializing recommender...")
    success = initialize_recommender(num_recipes=5, max_recipes=100)
    
    if success:
        print("✅ Recommender initialized successfully")
    else:
        print("❌ Failed to initialize recommender")
    
    # Test a simple route
    with app.test_client() as client:
        print("🔄 Testing routes...")
        
        # Test home route
        response = client.get('/')
        print(f"Home route (/): {response.status_code}")
        
        # Test welcome route
        response = client.get('/welcome')
        print(f"Welcome route (/welcome): {response.status_code}")
        
        # Test login route
        response = client.get('/login')
        print(f"Login route (/login): {response.status_code}")
        
        # Test API test route
        response = client.get('/api/test')
        print(f"API test route (/api/test): {response.status_code}")
        if response.status_code == 200:
            print(f"  Response: {response.get_json()}")
    
    print("\n🚀 Starting Flask development server...")
    app.run(debug=True, host='0.0.0.0', port=5000)
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
