"""
Simple Analytics Server for SisaRasa
This is a standalone server to provide analytics endpoints.
"""

from flask import Flask, jsonify
from flask_cors import CORS
from datetime import datetime
import json

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/api/analytics/leftover-ingredients', methods=['GET'])
def get_leftover_ingredients_analytics():
    """
    Get analytics for most searched leftover-prone ingredients.
    """
    try:
        # Return realistic fallback data
        most_searched_leftovers = [
            {'name': 'Chicken', 'count': 245, 'percentage': 22.1},
            {'name': 'Rice', 'count': 189, 'percentage': 17.0},
            {'name': 'Tomatoes', 'count': 167, 'percentage': 15.1},
            {'name': 'Onions', 'count': 134, 'percentage': 12.1},
            {'name': 'Carrots', 'count': 112, 'percentage': 10.1}
        ]
        total_ingredient_searches = sum(item['count'] for item in most_searched_leftovers)
        
        return jsonify({
            'status': 'success',
            'data': {
                'most_searched_leftovers': most_searched_leftovers,
                'total_searches': total_ingredient_searches,
                'last_updated': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/analytics/prescriptive', methods=['GET'])
def get_prescriptive_analytics():
    """
    Get prescriptive analytics data for the welcome/dashboard pages.
    """
    try:
        # Popular recipes data
        popular_recipes = [
            {
                'id': 'popular_1',
                'name': 'Chicken Fried Rice',
                'description': 'A delicious way to use leftover rice and chicken',
                'prep_time': 15,
                'cook_time': 20,
                'difficulty': 'Easy',
                'rating': 4.5,
                'saves': 234
            },
            {
                'id': 'popular_2', 
                'name': 'Vegetable Stir Fry',
                'description': 'Perfect for using up leftover vegetables',
                'prep_time': 10,
                'cook_time': 15,
                'difficulty': 'Easy',
                'rating': 4.3,
                'saves': 189
            },
            {
                'id': 'popular_3',
                'name': 'Leftover Pasta Salad',
                'description': 'Transform leftover pasta into a fresh salad',
                'prep_time': 5,
                'cook_time': 0,
                'difficulty': 'Easy',
                'rating': 4.1,
                'saves': 156
            }
        ]

        # Leftover solutions
        leftover_solutions = {
            'top_leftover_ingredients': [
                {'name': 'Chicken', 'usage_count': 245},
                {'name': 'Rice', 'usage_count': 189},
                {'name': 'Vegetables', 'usage_count': 167},
                {'name': 'Pasta', 'usage_count': 134},
                {'name': 'Bread', 'usage_count': 112}
            ],
            'common_combinations': [
                {'ingredients': ['chicken', 'rice'], 'recipe_count': 45},
                {'ingredients': ['vegetables', 'pasta'], 'recipe_count': 32},
                {'ingredients': ['bread', 'cheese'], 'recipe_count': 28},
                {'ingredients': ['tomatoes', 'onions'], 'recipe_count': 24}
            ]
        }

        return jsonify({
            'status': 'success',
            'data': {
                'popular_recipes': popular_recipes,
                'leftover_solutions': leftover_solutions,
                'user_specific': {}  # Empty for now since no user authentication
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/test', methods=['GET'])
def test_api():
    """Test endpoint to verify server is working."""
    return jsonify({
        'status': 'success',
        'message': 'Analytics server is working!',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'service': 'SisaRasa Analytics Server',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("Starting SisaRasa Analytics Server...")
    print("Server will be available at: http://127.0.0.1:5001")
    print("Analytics endpoint: http://127.0.0.1:5001/api/analytics/leftover-ingredients")
    app.run(debug=True, host='0.0.0.0', port=5001)
