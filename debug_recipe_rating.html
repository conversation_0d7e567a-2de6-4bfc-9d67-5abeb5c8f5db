<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Recipe Rating Issue</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .recipe-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .recipe-actions {
            margin-top: 10px;
        }
        .recipe-actions button {
            margin-right: 10px;
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .rate-btn {
            background-color: #ea5e18;
            color: white;
        }
        .debug-info {
            background: #e8f4f8;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <h1>🔍 Debug Recipe Rating Issue</h1>
        <p>This page simulates the exact scenario causing the rating error in your SisaRasa system.</p>
        
        <div class="debug-info">
            <strong>🐛 Debug Log:</strong><br>
            <div id="debugLog">Starting debug session...<br></div>
        </div>

        <!-- Test Recipe Cards -->
        <div class="recipe-card">
            <h3>✅ Test Recipe 1: Normal Recipe</h3>
            <p><strong>ID:</strong> recipe-123</p>
            <p><strong>Name:</strong> Test Salmon Recipe</p>
            <div class="recipe-actions">
                <button class="rate-btn" @click="testRating(normalRecipe)">Rate & Review</button>
            </div>
        </div>

        <div class="recipe-card">
            <h3>⚠️ Test Recipe 2: Recipe Without ID</h3>
            <p><strong>ID:</strong> (missing)</p>
            <p><strong>Name:</strong> Recipe Without ID</p>
            <div class="recipe-actions">
                <button class="rate-btn" @click="testRating(recipeWithoutId)">Rate & Review</button>
            </div>
        </div>

        <div class="recipe-card">
            <h3>❌ Test Recipe 3: Null Recipe</h3>
            <p><strong>ID:</strong> N/A</p>
            <p><strong>Name:</strong> N/A</p>
            <div class="recipe-actions">
                <button class="rate-btn" @click="testRating(null)">Rate & Review (NULL)</button>
            </div>
        </div>

        <div class="recipe-card">
            <h3>🎯 Test Recipe 4: Your Exact Scenario</h3>
            <p><strong>ID:</strong> {{ apiRecipe.id || 'missing' }}</p>
            <p><strong>Name:</strong> {{ apiRecipe.name || 'missing' }}</p>
            <p><em>This simulates the "5 Ingredient Maple Cardamom Salmon Fillet" recipe from your screenshot</em></p>
            <div class="recipe-actions">
                <button class="rate-btn" @click="testRating(apiRecipe)">Rate & Review</button>
            </div>
        </div>

        <div class="recipe-card">
            <h3>🔧 Test Recipe 5: Broken Recipe Object</h3>
            <p><strong>ID:</strong> undefined</p>
            <p><strong>Name:</strong> undefined</p>
            <div class="recipe-actions">
                <button class="rate-btn" @click="testRating(brokenRecipe)">Rate & Review</button>
            </div>
        </div>

        <button @click="clearLog" style="margin-top: 20px; padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px;">Clear Log</button>
        <button @click="runAllTests" style="margin-top: 20px; padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; margin-left: 10px;">Run All Tests</button>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    normalRecipe: {
                        id: 'recipe-123',
                        name: 'Test Salmon Recipe',
                        ingredients: ['salmon', 'maple syrup', 'cardamom'],
                        instructions: 'Cook the salmon with maple syrup and cardamom.'
                    },
                    recipeWithoutId: {
                        name: 'Recipe Without ID',
                        ingredients: ['ingredient1', 'ingredient2'],
                        instructions: 'Some instructions.'
                    },
                    apiRecipe: {
                        // This simulates the exact recipe from your screenshot
                        name: '5 Ingredient Maple Cardamom Salmon Fillet',
                        ingredients: ['salmon', 'maple syrup', 'cardamom', 'salt', 'pepper'],
                        instructions: 'Season salmon and cook with maple syrup.',
                        // Note: no 'id' property, which might be the issue
                        score: 0.67,
                        ingredient_match_percentage: 60
                    },
                    brokenRecipe: {
                        // This simulates a completely broken recipe object
                        id: undefined,
                        name: undefined,
                        ingredients: null
                    }
                }
            },
            methods: {
                log(message) {
                    const logDiv = document.getElementById('debugLog');
                    const timestamp = new Date().toLocaleTimeString();
                    logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
                    logDiv.scrollTop = logDiv.scrollHeight;
                },

                // UTILITY FUNCTION: Safely get recipe ID with comprehensive null checks
                safeGetRecipeId(recipe, context = 'unknown') {
                    this.log(`🔍 safeGetRecipeId called from ${context}`);
                    this.log(`📝 Recipe object: ${JSON.stringify(recipe, null, 2)}`);
                    
                    // Check if recipe exists
                    if (!recipe) {
                        this.log(`❌ ERROR [${context}]: Recipe is null or undefined`);
                        return null;
                    }
                    
                    // Check if recipe has an ID
                    if (recipe.id) {
                        this.log(`✅ SUCCESS [${context}]: Using recipe.id = ${recipe.id}`);
                        return recipe.id;
                    }
                    
                    // Check if recipe has a name to generate ID from
                    if (recipe.name && typeof recipe.name === 'string' && recipe.name.trim()) {
                        const generatedId = recipe.name.toLowerCase().replace(/\s+/g, '-');
                        this.log(`✅ SUCCESS [${context}]: Generated ID from name = ${generatedId}`);
                        return generatedId;
                    }
                    
                    this.log(`❌ ERROR [${context}]: Cannot generate recipe ID - no usable properties found`);
                    return null;
                },

                // UTILITY FUNCTION: Safely validate recipe object
                validateRecipe(recipe, context = 'unknown') {
                    this.log(`🔍 validateRecipe called from ${context}`);
                    this.log(`📝 Recipe object: ${JSON.stringify(recipe, null, 2)}`);
                    
                    if (!recipe) {
                        this.log(`❌ ERROR [${context}]: Recipe is null or undefined`);
                        return false;
                    }
                    
                    if (!recipe.name || typeof recipe.name !== 'string' || !recipe.name.trim()) {
                        this.log(`❌ ERROR [${context}]: Recipe missing valid name property`);
                        return false;
                    }
                    
                    this.log(`✅ SUCCESS [${context}]: Recipe validation passed`);
                    return true;
                },

                testRating(recipe) {
                    this.log(`\n🎯 === Testing Rating for Recipe ===`);
                    this.log(`📥 Input recipe: ${JSON.stringify(recipe, null, 2)}`);

                    // Use utility function to validate recipe
                    if (!this.validateRecipe(recipe, 'testRating')) {
                        this.log(`❌ Validation failed - showing error dialog`);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Recipe data is incomplete. Please try again.',
                            confirmButtonColor: '#ea5e18'
                        });
                        return;
                    }

                    // Get safe recipe ID
                    const recipeId = this.safeGetRecipeId(recipe, 'testRating');
                    if (!recipeId) {
                        this.log(`❌ ID generation failed - showing error dialog`);
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Recipe ID could not be determined. Please try again.',
                            confirmButtonColor: '#ea5e18'
                        });
                        return;
                    }

                    // Create a clean copy of the recipe with guaranteed properties
                    const cleanRecipe = {
                        id: recipeId,
                        name: recipe.name,
                        ingredients: recipe.ingredients || 'Ingredients not available',
                        instructions: recipe.instructions || 'Instructions not available',
                        // Copy other properties if they exist
                        ...recipe
                    };

                    this.log(`✅ SUCCESS: Clean recipe created: ${JSON.stringify(cleanRecipe, null, 2)}`);

                    Swal.fire({
                        icon: 'success',
                        title: '🎉 Rating Modal Would Open Successfully!',
                        html: `
                            <div style="text-align: left;">
                                <p><strong>Recipe:</strong> ${cleanRecipe.name}</p>
                                <p><strong>ID:</strong> ${cleanRecipe.id}</p>
                                <p><strong>Status:</strong> ✅ All validations passed</p>
                                <p style="color: #28a745;"><em>This would normally open the rating modal without errors.</em></p>
                            </div>
                        `,
                        confirmButtonColor: '#ea5e18'
                    });
                },

                runAllTests() {
                    this.log(`\n🚀 === Running All Tests ===`);
                    const recipes = [
                        { name: 'Normal Recipe', recipe: this.normalRecipe },
                        { name: 'Recipe Without ID', recipe: this.recipeWithoutId },
                        { name: 'Null Recipe', recipe: null },
                        { name: 'Your Exact Scenario', recipe: this.apiRecipe },
                        { name: 'Broken Recipe', recipe: this.brokenRecipe }
                    ];

                    recipes.forEach((test, index) => {
                        setTimeout(() => {
                            this.log(`\n📋 Test ${index + 1}: ${test.name}`);
                            this.testRating(test.recipe);
                        }, index * 1000);
                    });
                },

                clearLog() {
                    document.getElementById('debugLog').innerHTML = 'Log cleared...<br>';
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
