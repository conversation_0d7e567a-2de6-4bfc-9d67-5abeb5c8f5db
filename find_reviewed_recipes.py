#!/usr/bin/env python3
"""
Find recipes with synthetic review data for testing.
"""

import pymongo
import json

def find_reviewed_recipes():
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa

    # Get recipes with the most reviews
    pipeline = [
        {'$group': {
            '_id': '$recipe_id',
            'review_count': {'$sum': 1},
            'avg_rating': {'$avg': '$rating'},
            'sample_review': {'$first': '$review_text'}
        }},
        {'$sort': {'review_count': -1}},
        {'$limit': 15}
    ]

    top_reviewed_recipes = list(db.recipe_reviews.aggregate(pipeline))

    print('🔍 Top Recipes with Synthetic Reviews:')
    print('=' * 60)

    # Load recipe data to get names and ingredients
    try:
        with open('data/clean_recipes.json', 'r', encoding='utf-8') as f:
            recipes = json.load(f)
        
        recipe_lookup = {str(recipe.get('id')): recipe for recipe in recipes}
        
        for i, recipe_data in enumerate(top_reviewed_recipes, 1):
            recipe_id = recipe_data['_id']
            review_count = recipe_data['review_count']
            avg_rating = round(recipe_data['avg_rating'], 1)
            sample_review = recipe_data['sample_review'][:100] + '...' if len(recipe_data['sample_review']) > 100 else recipe_data['sample_review']
            
            # Get recipe details
            recipe_info = recipe_lookup.get(str(recipe_id))
            if recipe_info:
                recipe_name = recipe_info['name']
                ingredients = recipe_info.get('ingredients', [])[:4]
                
                print(f'{i}. "{recipe_name}"')
                print(f'   🆔 Recipe ID: {recipe_id}')
                print(f'   ⭐ {review_count} reviews | {avg_rating}★ average')
                print(f'   🥘 Key ingredients: {", ".join(ingredients)}')
                print(f'   💬 Sample review: "{sample_review}"')
                print()
                
                # Suggest search terms
                if i <= 5:
                    search_ingredients = [ing for ing in ingredients if len(ing) > 2 and ing.lower() not in ['salt', 'pepper', 'oil', 'water']][:3]
                    if search_ingredients:
                        print(f'   🔍 Try searching: {", ".join(search_ingredients)}')
                        print()
            else:
                print(f'{i}. Recipe ID: {recipe_id} (name not found)')
                print(f'   ⭐ {review_count} reviews | {avg_rating}★ average')
                print()
        
        print('\n🎯 QUICK TEST SUGGESTIONS:')
        print('=' * 60)
        print('Try these ingredient combinations to find reviewed recipes:')
        
        # Get common ingredients from top reviewed recipes
        common_ingredients = {}
        for recipe_data in top_reviewed_recipes[:10]:
            recipe_id = recipe_data['_id']
            recipe_info = recipe_lookup.get(str(recipe_id))
            if recipe_info:
                for ingredient in recipe_info.get('ingredients', []):
                    ingredient_clean = ingredient.lower().strip()
                    if len(ingredient_clean) > 2 and ingredient_clean not in ['salt', 'pepper', 'oil', 'water', 'sugar']:
                        common_ingredients[ingredient_clean] = common_ingredients.get(ingredient_clean, 0) + 1
        
        # Sort by frequency
        sorted_ingredients = sorted(common_ingredients.items(), key=lambda x: x[1], reverse=True)[:10]
        
        print('\nMost common ingredients in reviewed recipes:')
        for ingredient, count in sorted_ingredients:
            print(f'• {ingredient} (appears in {count} reviewed recipes)')
        
        print('\n🔍 RECOMMENDED SEARCHES:')
        print('Try searching for these combinations:')
        top_ingredients = [item[0] for item in sorted_ingredients[:6]]
        print(f'• {top_ingredients[0]}, {top_ingredients[1]}')
        print(f'• {top_ingredients[2]}, {top_ingredients[3]}')
        print(f'• {top_ingredients[0]}, {top_ingredients[2]}, {top_ingredients[4]}')
        
    except Exception as e:
        print(f'Error loading recipe data: {e}')
        print('\nShowing recipe IDs only:')
        for i, recipe_data in enumerate(top_reviewed_recipes, 1):
            recipe_id = recipe_data['_id']
            review_count = recipe_data['review_count']
            avg_rating = round(recipe_data['avg_rating'], 1)
            print(f'{i}. Recipe ID: {recipe_id} | {review_count} reviews | {avg_rating}★')

    client.close()

if __name__ == "__main__":
    find_reviewed_recipes()
