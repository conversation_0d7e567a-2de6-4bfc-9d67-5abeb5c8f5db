<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Review Submission Test</title>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .test-button {
            background: #ea5e18;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #d54e10;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Review Submission Fix Test</h1>
    
    <div class="test-section">
        <h2>Test Scenarios</h2>
        <p>This page tests the review submission functionality with various edge cases:</p>
        
        <button class="test-button" onclick="testNormalRecipe()">Test Normal Recipe</button>
        <button class="test-button" onclick="testRecipeWithoutId()">Test Recipe Without ID</button>
        <button class="test-button" onclick="testNullRecipe()">Test Null Recipe</button>
        <button class="test-button" onclick="testEmptyRecipe()">Test Empty Recipe</button>
        <button class="test-button" onclick="testRecipeWithMissingProperties()">Test Missing Properties</button>
        <button class="test-button" onclick="testAllScenarios()">Run All Tests</button>
        <button class="test-button" onclick="clearLog()">Clear Log</button>
    </div>
    
    <div class="test-section">
        <h2>Test Log</h2>
        <div id="testLog" class="log">Ready to test...\n</div>
    </div>

    <script>
        // Mock Vue.js-like object for testing
        const testApp = {
            selectedRecipeForRating: null,
            currentRating: 4,
            currentReviewText: 'Test review text',
            showRatingModalFlag: false,
            markAsVerified: false,
            verificationNotes: '',
            
            log(message) {
                const logElement = document.getElementById('testLog');
                const timestamp = new Date().toLocaleTimeString();
                logElement.textContent += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
            },
            
            showRatingModal(recipe) {
                this.log('=== showRatingModal called ===');
                this.log(`Input recipe: ${JSON.stringify(recipe)}`);

                // Enhanced validation for recipe data
                if (!recipe) {
                    this.log('ERROR: No recipe provided to showRatingModal');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Recipe data not available. Please try again.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                // Validate recipe has required properties
                if (!recipe.name) {
                    this.log('ERROR: Recipe missing name property');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Recipe data is incomplete. Please try again.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                // Create a clean copy of the recipe with guaranteed properties
                const cleanRecipe = {
                    id: recipe.id || recipe.name.toLowerCase().replace(/\s+/g, '-'),
                    name: recipe.name,
                    ingredients: recipe.ingredients || 'Ingredients not available',
                    instructions: recipe.instructions || 'Instructions not available',
                    // Copy other properties if they exist
                    ...recipe
                };

                this.log(`Clean recipe created: ${JSON.stringify(cleanRecipe)}`);
                this.selectedRecipeForRating = cleanRecipe;
                this.showRatingModalFlag = true;
                this.log('Recipe modal would be shown now');
            },
            
            submitRating() {
                this.log('=== submitRating called ===');
                this.log(`currentRating: ${this.currentRating}`);
                this.log(`selectedRecipeForRating: ${JSON.stringify(this.selectedRecipeForRating)}`);

                if (this.currentRating === 0) {
                    this.log('ERROR: Rating is 0');
                    Swal.fire({
                        icon: 'warning',
                        title: 'Rating Required',
                        text: 'Please select a rating before submitting',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                // Enhanced validation for selectedRecipeForRating
                if (!this.selectedRecipeForRating) {
                    this.log('ERROR: selectedRecipeForRating is null or undefined');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'No recipe selected. Please close this dialog and try again.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                // Validate recipe has required properties
                if (!this.selectedRecipeForRating.name) {
                    this.log('ERROR: selectedRecipeForRating missing name property');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Recipe data is incomplete. Please try again.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                // Generate recipe ID with better fallback logic
                let recipeId = null;
                if (this.selectedRecipeForRating.id) {
                    recipeId = this.selectedRecipeForRating.id;
                } else if (this.selectedRecipeForRating.name) {
                    recipeId = this.selectedRecipeForRating.name.toLowerCase().replace(/\s+/g, '-');
                } else {
                    this.log('ERROR: Cannot generate recipe ID - no id or name available');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Recipe ID could not be determined. Please try again.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                this.log(`Generated recipeId: ${recipeId}`);

                // Final validation of recipe ID
                if (!recipeId || recipeId.trim() === '') {
                    this.log('ERROR: Generated recipe ID is empty');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Recipe ID is invalid. Please try refreshing the page.',
                        confirmButtonColor: '#ea5e18'
                    });
                    return;
                }

                this.log('SUCCESS: All validations passed!');
                this.log(`Would submit review for recipe: ${recipeId}`);
                
                Swal.fire({
                    icon: 'success',
                    title: 'Test Passed!',
                    text: `Review validation successful for recipe: ${recipeId}`,
                    confirmButtonColor: '#ea5e18'
                });
            }
        };

        // Test functions
        function testNormalRecipe() {
            testApp.log('\n=== Testing Normal Recipe ===');
            const recipe = {
                id: 'test-recipe-123',
                name: 'Test Recipe',
                ingredients: ['egg', 'rice', 'soy sauce'],
                instructions: 'Cook everything together'
            };
            testApp.showRatingModal(recipe);
            setTimeout(() => testApp.submitRating(), 100);
        }

        function testRecipeWithoutId() {
            testApp.log('\n=== Testing Recipe Without ID ===');
            const recipe = {
                name: 'Recipe Without ID',
                ingredients: ['chicken', 'vegetables'],
                instructions: 'Cook the chicken and vegetables'
            };
            testApp.showRatingModal(recipe);
            setTimeout(() => testApp.submitRating(), 100);
        }

        function testNullRecipe() {
            testApp.log('\n=== Testing Null Recipe ===');
            testApp.showRatingModal(null);
        }

        function testEmptyRecipe() {
            testApp.log('\n=== Testing Empty Recipe ===');
            testApp.showRatingModal({});
        }

        function testRecipeWithMissingProperties() {
            testApp.log('\n=== Testing Recipe with Missing Properties ===');
            const recipe = {
                name: 'Recipe with Missing ID',
                // Missing ingredients, instructions, etc.
            };
            testApp.showRatingModal(recipe);
            setTimeout(() => testApp.submitRating(), 100);
        }

        function testAllScenarios() {
            testApp.log('\n=== Running All Test Scenarios ===');
            testNormalRecipe();
            setTimeout(() => {
                testRecipeWithoutId();
                setTimeout(() => {
                    testNullRecipe();
                    setTimeout(() => {
                        testEmptyRecipe();
                        setTimeout(() => {
                            testRecipeWithMissingProperties();
                            setTimeout(() => {
                                testApp.log('\n=== All Tests Completed ===');
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }

        function clearLog() {
            document.getElementById('testLog').textContent = 'Log cleared...\n';
        }
    </script>
</body>
</html>
