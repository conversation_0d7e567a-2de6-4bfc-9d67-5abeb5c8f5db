#!/usr/bin/env python3
"""
Test Community Features Fix
Verify that the database connection fix resolves the community issues
"""

import os
import sys
import json
import requests
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

class CommunityFixTester:
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:5000"
        self.mongo_uri = os.getenv('MONGO_URI')
        self.auth_token = None
        
    def log(self, message, level="INFO"):
        """Log test steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def login_with_existing_user(self):
        """Login with an existing user from the database."""
        self.log("\n🔐 TESTING LOGIN WITH EXISTING USER")
        self.log("=" * 60)
        
        try:
            # Get an existing user from Atlas
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            user = db.users.find_one({})
            
            if not user:
                self.log("❌ No users found in database", "ERROR")
                return False
                
            user_email = user.get('email')
            self.log(f"Found user: {user.get('name')} ({user_email})")
            
            # For testing, we'll create a test user with known credentials
            test_user_data = {
                "name": "Community Test User",
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            # Try to register the test user (will fail if exists, that's ok)
            try:
                register_response = requests.post(f"{self.api_base_url}/api/auth/register", 
                                                json=test_user_data)
                if register_response.status_code == 201:
                    self.log("✅ Test user registered successfully")
                else:
                    self.log("Test user already exists (that's fine)")
            except:
                pass
            
            # Now login with test user
            login_data = {
                "email": test_user_data["email"],
                "password": test_user_data["password"]
            }
            
            response = requests.post(f"{self.api_base_url}/api/auth/login", json=login_data)
            self.log(f"Login response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access_token')
                user_info = data.get('user', {})
                self.log(f"✅ Login successful")
                self.log(f"User ID: {user_info.get('id')}")
                self.log(f"User Name: {user_info.get('name')}")
                return True
            else:
                self.log(f"❌ Login failed: {response.text}", "ERROR")
                return False
                
            client.close()
            
        except Exception as e:
            self.log(f"❌ Login test failed: {e}", "ERROR")
            return False
            
    def test_community_post_creation_fixed(self):
        """Test creating a community post after the fix."""
        self.log("\n📝 TESTING COMMUNITY POST CREATION (AFTER FIX)")
        self.log("=" * 60)
        
        if not self.auth_token:
            self.log("❌ No auth token available", "ERROR")
            return False
            
        # Get initial counts
        try:
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            initial_count = db.community_posts.count_documents({})
            self.log(f"Initial posts count in Atlas: {initial_count}")
            client.close()
        except Exception as e:
            self.log(f"❌ Error getting initial count: {e}", "ERROR")
            return False
            
        # Create test post
        post_data = {
            "content": f"🧪 Test post after database fix - {datetime.now().isoformat()}"
        }
        
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(f"{self.api_base_url}/api/community/posts", 
                                   json=post_data, headers=headers)
            self.log(f"Post creation response status: {response.status_code}")
            
            if response.status_code == 200:
                post_response = response.json()
                self.log(f"✅ Post created successfully")
                self.log(f"Post ID: {post_response.get('id')}")
                self.log(f"User Name: {post_response.get('user_name')}")
                self.log(f"Content: {post_response.get('content')}")
                
                # Verify in Atlas database
                return self.verify_post_in_atlas(post_response.get('id'))
            else:
                self.log(f"❌ Post creation failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Post creation request failed: {e}", "ERROR")
            return False
            
    def verify_post_in_atlas(self, post_id):
        """Verify that the post was saved to Atlas database."""
        self.log("\n🔍 VERIFYING POST IN ATLAS DATABASE")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            post = db.community_posts.find_one({'_id': post_id})
            
            if post:
                self.log(f"✅ Post found in Atlas database!")
                self.log(f"Post content: {post.get('content')}")
                self.log(f"User ID: {post.get('user_id')}")
                self.log(f"Created at: {post.get('created_at')}")
                
                # Check final count
                final_count = db.community_posts.count_documents({})
                self.log(f"Final posts count in Atlas: {final_count}")
                
                client.close()
                return True
            else:
                self.log(f"❌ Post NOT found in Atlas database", "ERROR")
                client.close()
                return False
                
        except Exception as e:
            self.log(f"❌ Database verification failed: {e}", "ERROR")
            return False
            
    def test_comment_creation_fixed(self):
        """Test creating a comment after the fix."""
        self.log("\n💬 TESTING COMMENT CREATION (AFTER FIX)")
        self.log("=" * 60)
        
        if not self.auth_token:
            self.log("❌ No auth token available", "ERROR")
            return False
            
        try:
            # Get an existing post to comment on
            headers = {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(f"{self.api_base_url}/api/community/posts", headers=headers)
            if response.status_code != 200:
                self.log("❌ Could not fetch posts for commenting", "ERROR")
                return False
                
            posts = response.json()
            if not posts:
                self.log("❌ No posts available for commenting", "ERROR")
                return False
                
            post_id = posts[0].get('id')
            self.log(f"Commenting on post: {post_id}")
            
            # Create comment
            comment_data = {
                "content": f"🧪 Test comment after fix - {datetime.now().isoformat()}"
            }
            
            response = requests.post(f"{self.api_base_url}/api/community/posts/{post_id}/comments", 
                                   json=comment_data, headers=headers)
            self.log(f"Comment creation response status: {response.status_code}")
            
            if response.status_code == 200:
                comment_response = response.json()
                self.log(f"✅ Comment created successfully")
                self.log(f"Comment ID: {comment_response.get('id')}")
                self.log(f"User Name: {comment_response.get('user_name')}")
                self.log(f"Content: {comment_response.get('content')}")
                
                # Verify in database
                return self.verify_comment_in_atlas(comment_response.get('id'))
            else:
                self.log(f"❌ Comment creation failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Comment creation test failed: {e}", "ERROR")
            return False
            
    def verify_comment_in_atlas(self, comment_id):
        """Verify that the comment was saved to Atlas database."""
        self.log("\n🔍 VERIFYING COMMENT IN ATLAS DATABASE")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            comment = db.post_comments.find_one({'_id': comment_id})
            
            if comment:
                self.log(f"✅ Comment found in Atlas database!")
                self.log(f"Comment content: {comment.get('content')}")
                self.log(f"User ID: {comment.get('user_id')}")
                self.log(f"Post ID: {comment.get('post_id')}")
                client.close()
                return True
            else:
                self.log(f"❌ Comment NOT found in Atlas database", "ERROR")
                client.close()
                return False
                
        except Exception as e:
            self.log(f"❌ Comment verification failed: {e}", "ERROR")
            return False
            
    def run_fix_verification(self):
        """Run comprehensive test to verify the fix works."""
        self.log("🔧 COMMUNITY FEATURES FIX VERIFICATION")
        self.log("=" * 60)
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            'login_success': False,
            'post_creation_success': False,
            'post_in_atlas': False,
            'comment_creation_success': False,
            'comment_in_atlas': False
        }
        
        # Run tests
        results['login_success'] = self.login_with_existing_user()
        if results['login_success']:
            results['post_creation_success'] = self.test_community_post_creation_fixed()
            results['comment_creation_success'] = self.test_comment_creation_fixed()
        
        # Summary
        self.log("\n🎯 FIX VERIFICATION SUMMARY")
        self.log("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"{test_name.replace('_', ' ').title()}: {status}")
            
        if all(results.values()):
            self.log("\n🎉 ALL TESTS PASSED! Community features are now working correctly!")
            self.log("✅ Posts and comments are being saved to Atlas database")
            self.log("✅ User information is being properly attached")
        else:
            self.log("\n⚠️  Some tests failed. Please check the logs above.")
            
        return results

def main():
    """Run the fix verification test."""
    tester = CommunityFixTester()
    results = tester.run_fix_verification()
    
    # Save results
    with open('community_fix_verification.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    tester.log("\n💾 Results saved to: community_fix_verification.json")
    
    return results

if __name__ == "__main__":
    main()
