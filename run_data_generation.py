#!/usr/bin/env python3
"""
Quick runner script for synthetic data generation.
This script provides a simple interface to generate synthetic data for SisaRasa.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from generate_synthetic_data import SyntheticDataGenerator
    
    def quick_generate():
        """Quick generation with default settings."""
        print("🚀 SisaRasa Quick Data Generation")
        print("=" * 50)
        print("This will generate:")
        print("• 75 diverse users with different personas")
        print("• Realistic recipe reviews and ratings")
        print("• Search history and patterns")
        print("• Saved recipe collections")
        print("• Community interactions (votes)")
        print("• User analytics and engagement data")
        print()
        
        generator = SyntheticDataGenerator()
        generator.generate_all_data(75)
    
    def custom_generate():
        """Custom generation with user input."""
        print("🎯 SisaRasa Custom Data Generation")
        print("=" * 50)
        
        try:
            num_users = input("Number of users to generate (1-200, default 75): ").strip()
            num_users = int(num_users) if num_users else 75
            
            if num_users < 1 or num_users > 200:
                print("⚠️  Invalid range. Using default: 75")
                num_users = 75
                
        except ValueError:
            print("⚠️  Invalid input. Using default: 75")
            num_users = 75
        
        generator = SyntheticDataGenerator()
        generator.generate_all_data(num_users)
    
    def main():
        """Main menu."""
        print("SisaRasa Synthetic Data Generator")
        print("=" * 40)
        print("1. Quick Generate (75 users)")
        print("2. Custom Generate")
        print("3. Exit")
        print()
        
        choice = input("Select option (1-3): ").strip()
        
        if choice == "1":
            quick_generate()
        elif choice == "2":
            custom_generate()
        elif choice == "3":
            print("Goodbye!")
            return
        else:
            print("Invalid choice. Please run again.")
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nMake sure you have the required dependencies installed:")
    print("pip install pymongo bcrypt numpy")
    print("\nAlso ensure MongoDB is running on localhost:27017")
except Exception as e:
    print(f"❌ Error: {e}")
    print("\nPlease check your MongoDB connection and try again.")
