#!/usr/bin/env python3
"""
Show Test Recipes - Display specific recipes with reviews for UI testing
"""

import pymongo

def show_test_recipes():
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🍳 SISARASA RECIPES WITH REVIEWS - READY TO TEST!")
    print("=" * 60)
    
    # Get some recipes with reviews
    recipes_with_reviews = []
    
    # Get all recipe IDs that have reviews
    recipe_ids_with_reviews = db.recipe_reviews.distinct('recipe_id')
    
    # Get details for first 5 recipes
    count = 0
    for recipe_id in recipe_ids_with_reviews:
        if count >= 5:
            break

        recipe = db.recipes.find_one({'original_id': recipe_id})
        if recipe:
            review_count = db.recipe_reviews.count_documents({'recipe_id': recipe_id})
            reviews = list(db.recipe_reviews.find({'recipe_id': recipe_id}).limit(2))

            if reviews:  # Only include recipes that actually have reviews
                avg_rating = sum(r.get('rating', 0) for r in reviews) / len(reviews)

                recipes_with_reviews.append({
                    'id': recipe_id,
                    'name': recipe.get('name', 'Unknown'),
                    'ingredients': recipe.get('ingredients', [])[:3],  # First 3 ingredients
                    'review_count': review_count,
                    'avg_rating': avg_rating,
                    'sample_reviews': reviews
                })
                count += 1
    
    print("🧪 TEST THESE RECIPES IN YOUR UI:")
    print("-" * 60)
    
    for i, recipe in enumerate(recipes_with_reviews, 1):
        print(f"{i}. {recipe['name']}")
        print(f"   Recipe ID: {recipe['id']}")
        print(f"   Reviews: {recipe['review_count']} | Avg Rating: {recipe['avg_rating']:.1f}/5")
        
        if recipe['ingredients']:
            print(f"   🔍 Search with: {', '.join(recipe['ingredients'])}")
        
        # Show sample reviews
        if recipe['sample_reviews']:
            print(f"   📝 Sample Reviews:")
            for review in recipe['sample_reviews']:
                user = review.get('user_name', 'Anonymous')
                rating = review.get('rating', 0)
                text = review.get('review_text', '')[:40]
                print(f"      • {user}: {rating}/5 - \"{text}...\"")
        
        print()
    
    print("🎯 HOW TO TEST:")
    print("-" * 60)
    print("1. Start your SisaRasa application")
    print("2. Go to the dashboard")
    print("3. Search using the ingredients shown above (e.g., 'chicken, pepper, salt')")
    print("4. Find the recipe in the search results")
    print("5. Look for the review count on the recipe card (e.g., '8 reviews')")
    print("6. Click 'View Reviews' button to see all reviews")
    print("7. Verify that reviews display with user names, ratings, and text")
    
    print(f"\n✅ VERIFICATION COMPLETE!")
    print(f"   📊 {db.recipe_reviews.count_documents({})} reviews are now properly linked")
    print(f"   📖 {len(recipe_ids_with_reviews)} recipes have reviews")
    print(f"   🎉 Reviews should now appear in your UI!")
    
    client.close()

if __name__ == "__main__":
    show_test_recipes()
