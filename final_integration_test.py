#!/usr/bin/env python3
"""
Final comprehensive test to verify the recipe search integration is working.
"""

import requests
import json

def test_integration():
    """Comprehensive test of the recipe search integration."""
    
    print("🧪 COMPREHENSIVE RECIPE SEARCH INTEGRATION TEST")
    print("=" * 60)
    
    # Test 1: Search with ingredients that should match user recipes
    print("\n1️⃣ Testing search with user recipe ingredients...")
    
    test_cases = [
        {"ingredients": ["cabbage", "carrot"], "expected_user_recipes": True},
        {"ingredients": ["garlic", "soy sauce"], "expected_user_recipes": True},
        {"ingredients": ["noodle"], "expected_user_recipes": True},
        {"ingredients": ["chicken", "rice"], "expected_user_recipes": False},  # Likely no user recipes
    ]
    
    url = "http://localhost:5000/api/recommend"
    total_tests = 0
    passed_tests = 0
    
    for test_case in test_cases:
        total_tests += 1
        ingredients = test_case["ingredients"]
        expected = test_case["expected_user_recipes"]
        
        print(f"\n   🔍 Searching for: {', '.join(ingredients)}")
        
        try:
            response = requests.post(url, json={
                "ingredients": ingredients,
                "limit": 15
            })
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'ok':
                print(f"      ❌ API error: {data.get('message')}")
                continue
            
            recipes = data.get('recipes', [])
            user_recipes = [r for r in recipes if r.get('is_user_recipe', False)]
            system_recipes = [r for r in recipes if not r.get('is_user_recipe', False)]
            
            print(f"      📊 Results: {len(recipes)} total ({len(system_recipes)} system, {len(user_recipes)} user)")
            
            if len(user_recipes) > 0:
                print(f"      ✅ Found user recipes:")
                for recipe in user_recipes[:3]:  # Show first 3
                    score = recipe.get('score', 0)
                    print(f"         - {recipe.get('name', 'Unknown')} (score: {score:.3f})")
                passed_tests += 1
            else:
                if expected:
                    print(f"      ⚠️  Expected user recipes but found none")
                else:
                    print(f"      ✅ No user recipes found (as expected)")
                    passed_tests += 1
                    
        except Exception as e:
            print(f"      ❌ Error: {str(e)}")
    
    # Test 2: Verify immediate availability (no approval needed)
    print(f"\n2️⃣ Testing immediate availability (no approval workflow)...")
    
    try:
        # Check that all user recipes are available regardless of approval status
        response = requests.get("http://localhost:5000/api/community/recipes?limit=20",
                               headers={"Authorization": "Bearer dummy_token"})
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') == 'success':
            recipes = data.get('recipes', [])
            print(f"      📊 Community recipes available: {len(recipes)}")
            print(f"      ✅ All user recipes are immediately available (no approval required)")
            passed_tests += 1
            total_tests += 1
        else:
            print(f"      ❌ Community recipes API error: {data.get('message')}")
            
    except Exception as e:
        print(f"      ❌ Error testing community recipes: {str(e)}")
        total_tests += 1
    
    # Test 3: Verify ingredient search includes user recipe ingredients
    print(f"\n3️⃣ Testing ingredient search integration...")
    
    try:
        response = requests.get("http://localhost:5000/api/ingredients?search=cabbage&limit=10")
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') == 'ok':
            ingredients = data.get('ingredients', [])
            count = data.get('count', 0)
            print(f"      📊 Ingredient search results: {count} ingredients found")
            
            # Check if 'cabbage' is in the results (from user recipe)
            cabbage_found = any('cabbage' in str(ing).lower() for ing in ingredients)
            if cabbage_found:
                print(f"      ✅ User recipe ingredients included in search")
                passed_tests += 1
            else:
                print(f"      ⚠️  User recipe ingredients may not be included")
            total_tests += 1
        else:
            print(f"      ❌ Ingredient search error: {data.get('message')}")
            total_tests += 1
            
    except Exception as e:
        print(f"      ❌ Error testing ingredient search: {str(e)}")
        total_tests += 1
    
    # Summary
    print(f"\n" + "=" * 60)
    print(f"🏁 TEST SUMMARY")
    print(f"   Tests passed: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print(f"   🎉 ALL TESTS PASSED! Integration is working correctly.")
        print(f"\n✅ VERIFICATION COMPLETE:")
        print(f"   ✓ User-shared recipes are included in search results")
        print(f"   ✓ No approval workflow - recipes available immediately")
        print(f"   ✓ Search functionality works with both system and user recipes")
        print(f"   ✓ Ingredient search includes user recipe ingredients")
        return True
    else:
        print(f"   ⚠️  Some tests failed. Please review the results above.")
        return False

if __name__ == "__main__":
    success = test_integration()
    
    if success:
        print(f"\n🚀 INTEGRATION SUCCESSFUL!")
    else:
        print(f"\n❌ INTEGRATION NEEDS ATTENTION")
