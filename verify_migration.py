#!/usr/bin/env python3
"""
Post-Migration Verification Script for SisaRasa MongoDB Atlas Migration

This script verifies that the migration was successful by:
1. Testing Atlas connectivity
2. Comparing data counts between local and Atlas
3. Validating data integrity
4. Testing application functionality
5. Performance benchmarking
"""

import os
import sys
import time
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

class MigrationVerifier:
    def __init__(self):
        self.local_uri = 'mongodb://localhost:27017/sisarasa'
        self.atlas_uri = os.getenv('MONGO_URI')
        self.verification_results = {}
        
    def log(self, message, level="INFO"):
        """Log verification steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_atlas_connection(self):
        """Test connection to Atlas cluster."""
        self.log("\n🔗 TESTING ATLAS CONNECTION")
        self.log("=" * 60)
        
        if not self.atlas_uri:
            self.log("❌ MONGO_URI not found in environment variables", "ERROR")
            return False
            
        if 'mongodb+srv://' not in self.atlas_uri:
            self.log("❌ MONGO_URI doesn't appear to be an Atlas connection string", "ERROR")
            return False
            
        try:
            client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=5000)
            db = client.get_default_database()
            
            # Test connection
            start_time = time.time()
            client.admin.command('ping')
            connection_time = (time.time() - start_time) * 1000
            
            self.log(f"✅ Successfully connected to Atlas")
            self.log(f"📊 Connection time: {connection_time:.2f}ms")
            self.log(f"🗄️ Database name: {db.name}")
            
            # Get server info
            server_info = client.server_info()
            self.log(f"🔧 MongoDB version: {server_info['version']}")
            
            client.close()
            self.verification_results['atlas_connection'] = True
            return True
            
        except Exception as e:
            self.log(f"❌ Atlas connection failed: {e}", "ERROR")
            self.verification_results['atlas_connection'] = False
            return False
            
    def compare_data_counts(self):
        """Compare document counts between local and Atlas."""
        self.log("\n📊 COMPARING DATA COUNTS")
        self.log("=" * 60)
        
        try:
            # Connect to both databases
            local_client = MongoClient(self.local_uri, serverSelectionTimeoutMS=5000)
            atlas_client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=5000)
            
            local_db = local_client.get_default_database()
            atlas_db = atlas_client.get_default_database()
            
            # Get collections from local (excluding backups)
            local_collections = [c for c in local_db.list_collection_names() 
                               if 'backup' not in c.lower()]
            
            comparison_results = {}
            all_match = True
            
            for collection_name in local_collections:
                try:
                    local_count = local_db[collection_name].count_documents({})
                    atlas_count = atlas_db[collection_name].count_documents({}) if collection_name in atlas_db.list_collection_names() else 0
                    
                    comparison_results[collection_name] = {
                        'local': local_count,
                        'atlas': atlas_count,
                        'match': local_count == atlas_count
                    }
                    
                    if local_count == atlas_count:
                        self.log(f"✅ {collection_name}: {atlas_count:,} documents (matches)")
                    else:
                        self.log(f"❌ {collection_name}: Local={local_count:,}, Atlas={atlas_count:,}", "ERROR")
                        all_match = False
                        
                except Exception as e:
                    self.log(f"❌ Error comparing {collection_name}: {e}", "ERROR")
                    all_match = False
            
            local_client.close()
            atlas_client.close()
            
            self.verification_results['data_comparison'] = comparison_results
            self.verification_results['data_counts_match'] = all_match
            
            return all_match
            
        except Exception as e:
            self.log(f"❌ Data comparison failed: {e}", "ERROR")
            self.verification_results['data_counts_match'] = False
            return False
            
    def validate_data_integrity(self):
        """Validate data integrity in Atlas."""
        self.log("\n✅ VALIDATING DATA INTEGRITY")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.atlas_uri)
            db = client.get_default_database()
            
            integrity_checks = {}
            all_passed = True
            
            # Check 1: Users have required fields
            users_with_email = db.users.count_documents({'email': {'$exists': True, '$ne': ''}})
            total_users = db.users.count_documents({})
            
            if users_with_email == total_users and total_users > 0:
                self.log(f"✅ Users integrity: All {total_users} users have email addresses")
                integrity_checks['users_email'] = True
            else:
                self.log(f"❌ Users integrity: {users_with_email}/{total_users} users have emails", "ERROR")
                integrity_checks['users_email'] = False
                all_passed = False
            
            # Check 2: Recipes have required fields
            recipes_with_title = db.recipes.count_documents({'title': {'$exists': True, '$ne': ''}})
            total_recipes = db.recipes.count_documents({})
            
            if recipes_with_title == total_recipes and total_recipes > 0:
                self.log(f"✅ Recipes integrity: All {total_recipes} recipes have titles")
                integrity_checks['recipes_title'] = True
            else:
                self.log(f"❌ Recipes integrity: {recipes_with_title}/{total_recipes} recipes have titles", "ERROR")
                integrity_checks['recipes_title'] = False
                all_passed = False
            
            # Check 3: Reviews have valid ratings
            invalid_ratings = db.recipe_reviews.count_documents({
                '$or': [
                    {'rating': {'$lt': 1}},
                    {'rating': {'$gt': 5}},
                    {'rating': {'$exists': False}}
                ]
            })
            total_reviews = db.recipe_reviews.count_documents({})
            
            if invalid_ratings == 0 and total_reviews > 0:
                self.log(f"✅ Reviews integrity: All {total_reviews} reviews have valid ratings (1-5)")
                integrity_checks['reviews_rating'] = True
            else:
                self.log(f"❌ Reviews integrity: {invalid_ratings} invalid ratings found", "ERROR")
                integrity_checks['reviews_rating'] = False
                all_passed = False
            
            # Check 4: Foreign key relationships
            # Users referenced in reviews exist
            review_user_ids = db.recipe_reviews.distinct('user_id')
            existing_user_ids = db.users.distinct('_id')
            
            orphaned_reviews = len([uid for uid in review_user_ids if uid not in existing_user_ids])
            
            if orphaned_reviews == 0:
                self.log(f"✅ Referential integrity: All review users exist")
                integrity_checks['review_users'] = True
            else:
                self.log(f"❌ Referential integrity: {orphaned_reviews} orphaned reviews found", "ERROR")
                integrity_checks['review_users'] = False
                all_passed = False
            
            client.close()
            
            self.verification_results['integrity_checks'] = integrity_checks
            self.verification_results['data_integrity'] = all_passed
            
            return all_passed
            
        except Exception as e:
            self.log(f"❌ Data integrity validation failed: {e}", "ERROR")
            self.verification_results['data_integrity'] = False
            return False
            
    def test_performance(self):
        """Test basic performance of Atlas cluster."""
        self.log("\n⚡ TESTING PERFORMANCE")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.atlas_uri)
            db = client.get_default_database()
            
            performance_results = {}
            
            # Test 1: Simple query performance
            start_time = time.time()
            user_count = db.users.count_documents({})
            query_time = (time.time() - start_time) * 1000
            
            self.log(f"📊 User count query: {query_time:.2f}ms ({user_count:,} users)")
            performance_results['user_count_query'] = query_time
            
            # Test 2: Index usage test
            start_time = time.time()
            user_by_email = db.users.find_one({'email': {'$exists': True}})
            email_query_time = (time.time() - start_time) * 1000
            
            self.log(f"📊 Email lookup query: {email_query_time:.2f}ms")
            performance_results['email_query'] = email_query_time
            
            # Test 3: Aggregation performance
            start_time = time.time()
            rating_stats = list(db.recipe_reviews.aggregate([
                {'$group': {'_id': None, 'avg_rating': {'$avg': '$rating'}, 'count': {'$sum': 1}}}
            ]))
            aggregation_time = (time.time() - start_time) * 1000
            
            if rating_stats:
                avg_rating = rating_stats[0]['avg_rating']
                review_count = rating_stats[0]['count']
                self.log(f"📊 Rating aggregation: {aggregation_time:.2f}ms (avg: {avg_rating:.2f}, count: {review_count:,})")
            else:
                self.log(f"📊 Rating aggregation: {aggregation_time:.2f}ms (no data)")
            
            performance_results['aggregation_query'] = aggregation_time
            
            # Performance assessment
            if query_time < 100 and email_query_time < 50 and aggregation_time < 200:
                self.log("✅ Performance: Good response times")
                performance_results['assessment'] = 'good'
            elif query_time < 500 and email_query_time < 200 and aggregation_time < 1000:
                self.log("⚠️ Performance: Acceptable response times")
                performance_results['assessment'] = 'acceptable'
            else:
                self.log("❌ Performance: Slow response times", "WARNING")
                performance_results['assessment'] = 'slow'
            
            client.close()
            
            self.verification_results['performance'] = performance_results
            return True
            
        except Exception as e:
            self.log(f"❌ Performance testing failed: {e}", "ERROR")
            self.verification_results['performance'] = False
            return False
            
    def test_application_features(self):
        """Test key application features with Atlas."""
        self.log("\n🧪 TESTING APPLICATION FEATURES")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.atlas_uri)
            db = client.get_default_database()
            
            feature_tests = {}
            
            # Test 1: User authentication data
            users_with_passwords = db.users.count_documents({'password': {'$exists': True}})
            total_users = db.users.count_documents({})
            
            if users_with_passwords > 0:
                self.log(f"✅ Authentication: {users_with_passwords}/{total_users} users have password data")
                feature_tests['authentication'] = True
            else:
                self.log("❌ Authentication: No users with password data found", "ERROR")
                feature_tests['authentication'] = False
            
            # Test 2: Recipe recommendation data
            recipes_with_cuisine = db.recipes.count_documents({'cuisine': {'$exists': True}})
            total_recipes = db.recipes.count_documents({})
            
            if recipes_with_cuisine > 0:
                self.log(f"✅ Recommendations: {recipes_with_cuisine}/{total_recipes} recipes have cuisine data")
                feature_tests['recommendations'] = True
            else:
                self.log("❌ Recommendations: No recipes with cuisine data", "ERROR")
                feature_tests['recommendations'] = False
            
            # Test 3: Community features
            community_posts = db.community_posts.count_documents({})
            post_comments = db.post_comments.count_documents({})
            
            if community_posts > 0 or post_comments > 0:
                self.log(f"✅ Community: {community_posts} posts, {post_comments} comments")
                feature_tests['community'] = True
            else:
                self.log("⚠️ Community: No community data found", "WARNING")
                feature_tests['community'] = False
            
            # Test 4: Review system
            reviews_with_votes = db.review_votes.count_documents({})
            total_reviews = db.recipe_reviews.count_documents({})
            
            if total_reviews > 0:
                self.log(f"✅ Reviews: {total_reviews} reviews, {reviews_with_votes} votes")
                feature_tests['reviews'] = True
            else:
                self.log("❌ Reviews: No review data found", "ERROR")
                feature_tests['reviews'] = False
            
            client.close()
            
            self.verification_results['feature_tests'] = feature_tests
            return True
            
        except Exception as e:
            self.log(f"❌ Feature testing failed: {e}", "ERROR")
            self.verification_results['feature_tests'] = False
            return False
            
    def generate_verification_report(self):
        """Generate a comprehensive verification report."""
        self.log("\n📋 VERIFICATION REPORT")
        self.log("=" * 60)
        
        # Overall status
        all_tests_passed = all([
            self.verification_results.get('atlas_connection', False),
            self.verification_results.get('data_counts_match', False),
            self.verification_results.get('data_integrity', False)
        ])
        
        if all_tests_passed:
            self.log("🎉 MIGRATION VERIFICATION: PASSED")
            self.log("✅ Your SisaRasa application is ready to use with Atlas!")
        else:
            self.log("⚠️ MIGRATION VERIFICATION: ISSUES FOUND")
            self.log("❌ Please review the issues above before using Atlas")
        
        # Save detailed report
        report_file = f"migration_verification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w') as f:
                json.dump(self.verification_results, f, indent=2, default=str)
            self.log(f"📄 Detailed report saved: {report_file}")
        except Exception as e:
            self.log(f"❌ Failed to save report: {e}", "ERROR")
        
        return all_tests_passed
        
    def run_verification(self):
        """Run complete verification process."""
        self.log("🔍 MONGODB ATLAS MIGRATION VERIFICATION")
        self.log("=" * 60)
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run all verification tests
        tests = [
            ("Atlas Connection", self.test_atlas_connection),
            ("Data Counts", self.compare_data_counts),
            ("Data Integrity", self.validate_data_integrity),
            ("Performance", self.test_performance),
            ("Application Features", self.test_application_features)
        ]
        
        for test_name, test_func in tests:
            try:
                self.log(f"\n🔄 Running {test_name} test...")
                test_func()
            except Exception as e:
                self.log(f"❌ {test_name} test failed: {e}", "ERROR")
        
        # Generate final report
        return self.generate_verification_report()

def main():
    """Main verification function."""
    verifier = MigrationVerifier()
    
    print("🔍 SisaRasa MongoDB Atlas Migration Verification")
    print("=" * 60)
    print("This tool verifies your Atlas migration was successful.")
    
    success = verifier.run_verification()
    
    if success:
        print("\n✅ Verification completed successfully!")
        print("Your migration to MongoDB Atlas is verified and ready for production.")
    else:
        print("\n⚠️ Verification found issues.")
        print("Please review the report and fix any issues before going live.")

if __name__ == "__main__":
    main()
