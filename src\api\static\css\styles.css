/*===== GOOGLE FONTS =====*/
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap");
/*===== VARIABLES CSS =====*/
:root{
  --header-height: 3rem;

  /*===== Colors =====*/
  --first-color: #f1ead1;
  --first-color-dark: #e1cc7f;
  --first-color-darken: #f9e59a;
  --white-color: #0b0a0a;

  /*===== Font and typography =====*/
  --body-font: 'Poppins', sans-serif;
  --big-font-size: 2.5rem;
  --normal-font-size: .938rem;

  /*===== z index =====*/
  --z-fixed: 100;
}

@media screen and (min-width: 768px){
  :root{
    --big-font-size: 5rem;
    --normal-font-size: 1rem;
  }
}

/*===== BASE =====*/
*,::before,::after{
  box-sizing: border-box;
}

body{
  margin: var(--header-height) 0 0 0;
  padding: 0;
  font-family: var(--body-font);
  font-size: var(--normal-font-size);
  font-weight: 500;
}

h1,p,ul{
  margin: 0;
}

ul{
  padding: 0;
  list-style: none;
}

a{
  text-decoration: none;
}

img{
  max-width: 100%;
  height: auto;
}

/*===== LAYOUT =====*/
.bd-grid{
  max-width: 1024px;
  display: grid;
  grid-template-columns: 100%;
  column-gap: 2rem;
  width: calc(100% - 2rem);
  margin-left: 1rem;
  margin-right: 1rem;
}

.l-header{
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  background-color: var(--first-color);
}

/*===== NAV =====*/
.nav{
  height: var(--header-height);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

@media screen and (max-width: 768px){
  .nav__menu{
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    height: 100%;
    padding: 3.5rem 1.5rem 0;
    background: rgba(255,255,255,.3);
    backdrop-filter: blur(10px);
    transition: .5s;
  }
}

.nav__close{
  position: absolute;
  top: .75rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
}

.nav__item{
  margin-bottom: 2rem;
}

.nav__close, .nav__link, .nav__logo, .nav__toggle{
  color: var(--white-color);
}

.nav__link:hover{
  color: var(--first-color-dark);
}

.nav__toggle{
  font-size: 1.5rem;
  cursor: pointer;
}

/*=== Show menu ===*/
.show{
  right: 0;
}

/*===== HOME =====*/
.home{
  background-color: var(--first-color);
  overflow: hidden;
}

.home__container{
  height: calc(100vh - var(--header-height));
  grid-template-rows: repeat(2, max-content);
  row-gap: 1.5rem;
}

.home__img{
  position: relative;
  padding-top: 1.5rem;
  justify-self: center;
  width: 302px;
  height: 233px;
}

.home__img img{
  position: absolute;
  top: 0;
  left: 0;
}

.home__data{
  color: var(--white-color);
}

.home__title{
  font-size: var(--big-font-size);
  line-height: 1.3;
  margin-bottom: 1rem;
}

.home__description{
  margin-bottom: 2.5rem;
}

.home__button{
  display: inline-block;
  background-color: var(--first-color-dark);
  color: var(--white-color);
  padding: 1.125rem 2rem;
  border-radius: .5rem;
}

.home__button:hover{
  background-color: var(--first-color-darken);
}

/* ===== MEDIA QUERIES =====*/
@media screen and (min-width: 768px){
  body{
    margin: 0;
  }

  .nav{
    height: calc(var(--header-height) + 1.5rem);
  }

  .nav__toggle, .nav__close{
    display: none;
  }

  .nav__list{
    display: flex;
  }

  .nav__item{
    margin-left: 3rem;
    margin-bottom: 0;
  }

  .home__container{
    height: 100vh;
    grid-template-columns: repeat(2, max-content);
    grid-template-rows:  1fr;
    row-gap: 0;
    align-items: center;
    justify-content: center;
  }

  .home__img{
    order: 1;
    width: 375px;
    height: 289px;
  }

  .home__img img{
    width: 375px;
  }
}

@media screen and (min-width: 1024px){
  .bd-grid{
    margin-left: auto;
    margin-right: auto;
  }

  .home__container{
    justify-content: initial;
    column-gap: 4.5rem;
  }

  .home__img{
    width: 604px;
    height: 466px;
  }

  .home__img img{
    width: 604px;
  }
}

/* ===== ABOUT =====*/
.about {
  padding: 4rem 0;
  background-color: #fff;
}

.about__container {
  row-gap: 2rem;
}

.about__data {
  padding: 2rem;
  background-color: var(--first-color);
  border-radius: 0.5rem;
  text-align: center;
  color: var(--white-color);
}

.about__description {
  margin-bottom: 1.5rem;
  text-align: justify;
}

/* ===== CONTACT =====*/
.contact {
  padding: 4rem 0;
  background-color: #fff;
}

.contact__container {
  row-gap: 2rem;
}

.section__title {
  font-size: 2rem;
  color: var(--white-color);
  margin-bottom: 1.5rem;
  text-align: center;
}

.contact__description {
  text-align: center;
  margin-bottom: 2rem;
}

.contact__info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 2rem;
}

.contact__item {
  display: flex;
  align-items: center;
  column-gap: 0.5rem;
  margin-bottom: 1rem;
}

.contact__item i {
  font-size: 1.5rem;
  color: var(--first-color-dark);
}

.contact__data {
  padding: 2rem;
  background-color: var(--first-color);
  border-radius: 0.5rem;
  text-align: center;
  color: var(--white-color);
}