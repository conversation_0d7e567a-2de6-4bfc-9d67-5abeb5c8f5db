<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>Profile API Test Page</h1>
    
    <div class="test-section">
        <h2>1. Login</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="testpassword123">
        <button onclick="testLogin()">Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test /api/auth/me</h2>
        <button onclick="testMeEndpoint()">Test /me Endpoint</button>
        <div id="meResult"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test /api/auth/profile-image/current</h2>
        <button onclick="testProfileImageEndpoint()">Test Profile Image Endpoint</button>
        <div id="profileImageResult"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test Profile Image Display</h2>
        <div id="imageDisplay"></div>
    </div>

    <script>
        let currentToken = null;
        
        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.status === 'success') {
                    currentToken = data.token;
                    resultDiv.innerHTML = `<div class="success">✅ Login successful!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Login failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testMeEndpoint() {
            const resultDiv = document.getElementById('meResult');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please login first</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const hasProfileImage = data.user && data.user.profile_image;
                    const status = hasProfileImage ? '✅ Profile image field present!' : '❌ Profile image field missing!';
                    
                    resultDiv.innerHTML = `<div class="${hasProfileImage ? 'success' : 'error'}">${status}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Request failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
        
        async function testProfileImageEndpoint() {
            const resultDiv = document.getElementById('profileImageResult');
            
            if (!currentToken) {
                resultDiv.innerHTML = '<div class="warning">⚠️ Please login first</div>';
                return;
            }
            
            try {
                const response = await fetch('/api/auth/profile-image/current', {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    const hasProfileImage = data.profile_image;
                    const status = hasProfileImage ? '✅ Profile image found!' : '❌ No profile image!';
                    
                    resultDiv.innerHTML = `<div class="${hasProfileImage ? 'success' : 'error'}">${status}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`;
                    
                    // Display the image
                    if (hasProfileImage) {
                        document.getElementById('imageDisplay').innerHTML = 
                            `<h3>Profile Image Preview:</h3>
                             <img src="${data.profile_image}" alt="Profile" style="width: 100px; height: 100px; border-radius: 50%; border: 2px solid #ccc;">`;
                    }
                } else if (response.status === 404) {
                    resultDiv.innerHTML = '<div class="warning">⚠️ No profile image found (404)</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Request failed: ${response.statusText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
