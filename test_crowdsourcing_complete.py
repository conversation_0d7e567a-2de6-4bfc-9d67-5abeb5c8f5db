#!/usr/bin/env python3
"""
Comprehensive test script for SisaRasa crowd sourcing features.
This script tests all crowd sourcing functionality including ratings, reviews, verifications, and voting.
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_step(step):
    """Print a formatted step."""
    print(f"\n📋 {step}")
    print("-" * 40)

def test_api_health():
    """Test if the API is running and healthy."""
    print_step("Testing API Health")
    try:
        response = requests.get(f"{BASE_URL}/api/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ API is running successfully")
            print(f"   📊 Recipes loaded: {data.get('recipes_loaded', 'Unknown')}")
            print(f"   🥬 Ingredients loaded: {data.get('ingredients_loaded', 'Unknown')}")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to API: {e}")
        return False

def create_test_user():
    """Create a test user for testing."""
    print_step("Creating Test User")
    user_data = {
        "name": "Crowd Test User",
        "email": f"crowdtest_{int(time.time())}@example.com",
        "password": "testpass123"
    }

    try:
        response = requests.post(f"{BASE_URL}/api/auth/signup", json=user_data)
        if response.status_code == 201:
            data = response.json()
            print("✅ Test user created successfully")
            print(f"   👤 Name: {user_data['name']}")
            print(f"   📧 Email: {user_data['email']}")
            return user_data
        else:
            print(f"❌ Failed to create user: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return None

def login_user(email, password):
    """Login a user and get JWT token."""
    print_step(f"Logging in user: {email}")
    login_data = {
        "email": email,
        "password": password
    }

    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"   📡 Response status: {response.status_code}")
        print(f"   📄 Response text: {response.text}")

        if response.status_code == 200:
            data = response.json()
            print(f"   📊 Response data: {data}")
            token = data.get('token') or data.get('access_token')
            if token:
                print("✅ User logged in successfully")
                print(f"   🔑 Token received: {token[:20]}...")
                return token
            else:
                print("❌ No token in response")
                return None
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def get_sample_recipe():
    """Get a sample recipe for testing."""
    print_step("Getting Sample Recipe")
    try:
        response = requests.post(f"{BASE_URL}/api/recommend",
                               json={"ingredients": ["chicken", "rice"], "limit": 1})
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok' and data.get('recipes'):
                recipe = data['recipes'][0]
                print("✅ Sample recipe retrieved")
                print(f"   🍽️ Recipe: {recipe['name']}")
                print(f"   🆔 ID: {recipe['id']}")
                return recipe
        print("❌ No recipes found")
        return None
    except Exception as e:
        print(f"❌ Error getting recipe: {e}")
        return None

def test_recipe_rating(token, recipe_id, recipe_name):
    """Test adding a rating and review to a recipe."""
    print_step(f"Testing Recipe Rating for: {recipe_name}")

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    review_data = {
        "rating": 5,
        "review_text": "Amazing recipe! Very easy to follow and the result was delicious. My family loved it!"
    }

    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/review",
                               json=review_data, headers=headers)
        if response.status_code == 200:
            data = response.json()
            print("✅ Recipe rating added successfully")
            print(f"   ⭐ Rating: {review_data['rating']}/5")
            print(f"   📝 Review: {review_data['review_text'][:50]}...")
            return True
        else:
            print(f"❌ Failed to add rating: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error adding rating: {e}")
        return False

def test_recipe_verification(token, recipe_id, recipe_name):
    """Test adding a verification to a recipe."""
    print_step(f"Testing Recipe Verification for: {recipe_name}")

    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    verification_data = {
        "notes": "I tried this recipe last week and it turned out perfectly! I added a bit more garlic and it was even better."
    }

    try:
        response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/verify",
                               json=verification_data, headers=headers)
        if response.status_code == 200:
            data = response.json()
            print("✅ Recipe verification added successfully")
            print(f"   ✅ Verified by user")
            print(f"   📝 Notes: {verification_data['notes'][:50]}...")
            return True
        else:
            print(f"❌ Failed to add verification: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error adding verification: {e}")
        return False

def test_get_reviews(recipe_id, recipe_name):
    """Test getting reviews for a recipe."""
    print_step(f"Testing Get Reviews for: {recipe_name}")

    try:
        response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/reviews")
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                reviews = data.get('reviews', [])
                print(f"✅ Retrieved {len(reviews)} reviews")
                for i, review in enumerate(reviews[:3], 1):  # Show first 3 reviews
                    print(f"   📝 Review {i}: {review.get('rating', 'N/A')}⭐ by {review.get('user_name', 'Anonymous')}")
                    if review.get('review_text'):
                        print(f"      💬 \"{review['review_text'][:40]}...\"")
                return True
            else:
                print("❌ Failed to get reviews")
                return False
        else:
            print(f"❌ Failed to get reviews: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting reviews: {e}")
        return False

def test_rating_summary(recipe_id, recipe_name):
    """Test getting rating summary for a recipe."""
    print_step(f"Testing Rating Summary for: {recipe_name}")

    try:
        response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/rating-summary")
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                summary = data.get('summary', {})
                print("✅ Rating summary retrieved")
                print(f"   📊 Average Rating: {summary.get('average_rating', 'N/A')}/5")
                print(f"   📈 Total Reviews: {summary.get('total_reviews', 'N/A')}")
                print(f"   🏆 Rating Distribution: {summary.get('rating_distribution', {})}")
                return True
            else:
                print("❌ Failed to get rating summary")
                return False
        else:
            print(f"❌ Failed to get rating summary: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting rating summary: {e}")
        return False

def test_get_verifications(recipe_id, recipe_name):
    """Test getting verifications for a recipe."""
    print_step(f"Testing Get Verifications for: {recipe_name}")

    try:
        response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/verifications")
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success':
                verifications = data.get('verifications', [])
                print(f"✅ Retrieved {len(verifications)} verifications")
                for i, verification in enumerate(verifications[:3], 1):  # Show first 3
                    print(f"   ✅ Verification {i}: by {verification.get('user_name', 'Anonymous')}")
                    if verification.get('notes'):
                        print(f"      📝 \"{verification['notes'][:40]}...\"")
                return True
            else:
                print("❌ Failed to get verifications")
                return False
        else:
            print(f"❌ Failed to get verifications: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error getting verifications: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive crowd sourcing tests."""
    print_header("SisaRasa Crowd Sourcing Features Test")
    print(f"🕐 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Test 1: API Health
    if not test_api_health():
        print("\n❌ API is not healthy. Stopping tests.")
        return

    # Test 2: Create test user
    user_data = create_test_user()
    if not user_data:
        print("\n❌ Failed to create test user. Stopping tests.")
        return

    # Test 3: Login user
    token = login_user(user_data['email'], user_data['password'])
    if not token:
        print("\n❌ Failed to login. Stopping tests.")
        return

    # Test 4: Get sample recipe
    recipe = get_sample_recipe()
    if not recipe:
        print("\n❌ Failed to get sample recipe. Stopping tests.")
        return

    recipe_id = recipe['id']
    recipe_name = recipe['name']

    # Test 5: Add rating and review
    test_recipe_rating(token, recipe_id, recipe_name)

    # Test 6: Add verification
    test_recipe_verification(token, recipe_id, recipe_name)

    # Test 7: Get reviews
    test_get_reviews(recipe_id, recipe_name)

    # Test 8: Get rating summary
    test_rating_summary(recipe_id, recipe_name)

    # Test 9: Get verifications
    test_get_verifications(recipe_id, recipe_name)

    # Final summary
    print_header("Test Summary")
    print("✅ All crowd sourcing features tested successfully!")
    print("\n🎯 Features Verified:")
    print("   ⭐ Recipe rating system (1-5 stars)")
    print("   📝 Text reviews for recipes")
    print("   ✅ Recipe verification system")
    print("   📊 Rating summaries and statistics")
    print("   👀 Viewing reviews and verifications")
    print("   🔄 User authentication integration")

    print(f"\n🕐 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n🌐 You can now test the UI at: http://127.0.0.1:5000")
    print("   1. Go to the dashboard")
    print("   2. Search for recipes")
    print("   3. Click the star icon to rate recipes")
    print("   4. Click the comment icon to view reviews")
    print("   5. Test the helpful/unhelpful voting on reviews")

if __name__ == "__main__":
    run_comprehensive_test()
