<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recipe Sharing Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .recipe-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        .recipe-image {
            max-width: 200px;
            max-height: 150px;
            border-radius: 4px;
        }
        .debug-info {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Recipe Sharing Debug Test</h1>
    
    <div id="status">Loading...</div>
    
    <div id="api-response" class="debug-info">
        <h3>API Response:</h3>
        <pre id="raw-data"></pre>
    </div>
    
    <div id="recipes-container">
        <h3>Rendered Recipes:</h3>
    </div>

    <script>
        async function testRecipeSharing() {
            const statusDiv = document.getElementById('status');
            const rawDataPre = document.getElementById('raw-data');
            const recipesContainer = document.getElementById('recipes-container');
            
            try {
                statusDiv.innerHTML = '<span class="success">Fetching recipes...</span>';
                
                // Test API endpoint with auth token
                const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8BqbQgCs7jLD_N3ncbf-KHHat8diw7xnOuF6bE6FW9c';
                const response = await fetch('/api/shared-recipes', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                // Display raw API response
                rawDataPre.textContent = JSON.stringify(data, null, 2);
                
                statusDiv.innerHTML = `<span class="success">✅ API Success: Found ${data.length} recipes</span>`;
                
                // Render recipes
                if (data.length === 0) {
                    recipesContainer.innerHTML += '<p>No recipes found in database.</p>';
                } else {
                    data.forEach((recipe, index) => {
                        const recipeDiv = document.createElement('div');
                        recipeDiv.className = 'recipe-card';
                        
                        // Handle image
                        let imageHtml = '';
                        const imageUrl = recipe.image || recipe.image_data || '';
                        
                        if (imageUrl) {
                            let finalImageUrl = imageUrl;
                            
                            // If it's base64 data, ensure it has proper data URL prefix
                            if (imageUrl && !imageUrl.startsWith('http') && !imageUrl.startsWith('data:')) {
                                finalImageUrl = `data:image/png;base64,${imageUrl}`;
                            }
                            
                            imageHtml = `
                                <div>
                                    <img src="${finalImageUrl}" 
                                         alt="${recipe.name}" 
                                         class="recipe-image"
                                         onload="console.log('✅ Image loaded:', '${recipe.name}')"
                                         onerror="console.log('❌ Image failed:', '${recipe.name}', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';" />
                                    <div style="display:none; padding: 20px; background: #f0f0f0; text-align: center;">
                                        📷 No Image Available
                                    </div>
                                </div>
                            `;
                        } else {
                            imageHtml = '<div style="padding: 20px; background: #f0f0f0; text-align: center;">📷 No Image Available</div>';
                        }
                        
                        recipeDiv.innerHTML = `
                            <h4>${recipe.name}</h4>
                            <p><strong>Cuisine:</strong> ${recipe.cuisine}</p>
                            <p><strong>Description:</strong> ${recipe.description}</p>
                            <p><strong>By:</strong> ${recipe.username}</p>
                            ${imageHtml}
                            <div class="debug-info">
                                <strong>Debug Info:</strong><br>
                                Recipe ID: ${recipe.id}<br>
                                Has 'image' field: ${!!recipe.image}<br>
                                Has 'image_data' field: ${!!recipe.image_data}<br>
                                Image URL length: ${imageUrl.length}<br>
                                Final Image URL: ${imageUrl.substring(0, 100)}${imageUrl.length > 100 ? '...' : ''}
                            </div>
                        `;
                        
                        recipesContainer.appendChild(recipeDiv);
                    });
                }
                
            } catch (error) {
                console.error('Error testing recipe sharing:', error);
                statusDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                rawDataPre.textContent = `Error: ${error.message}`;
            }
        }
        
        // Run test when page loads
        document.addEventListener('DOMContentLoaded', testRecipeSharing);
    </script>
</body>
</html>
