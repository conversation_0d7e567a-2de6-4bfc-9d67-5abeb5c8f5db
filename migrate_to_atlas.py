#!/usr/bin/env python3
"""
Quick Migration Script for SisaRasa to MongoDB Atlas
Using your specific Atlas connection string
"""

import os
import sys
import subprocess
import time
from datetime import datetime
from pymongo import MongoClient

# Your Atlas connection string (with database name)
ATLAS_URI = "mongodb+srv://farahfiqh:<EMAIL>/sisarasa?retryWrites=true&w=majority&appName=SisaRasa"
LOCAL_URI = "mongodb://localhost:27017/sisarasa"

def log(message, level="INFO"):
    """Log migration steps."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {level}: {message}")

def test_local_connection():
    """Test local MongoDB connection."""
    log("🔍 Testing local MongoDB connection...")
    try:
        client = MongoClient(LOCAL_URI, serverSelectionTimeoutMS=5000)
        db = client.get_default_database()
        client.admin.command('ping')
        
        # Get basic stats
        collections = db.list_collection_names()
        total_docs = sum(db[col].count_documents({}) for col in collections)
        
        log(f"✅ Local database connected: {db.name}")
        log(f"📊 Collections: {len(collections)}, Documents: {total_docs:,}")
        
        client.close()
        return True
    except Exception as e:
        log(f"❌ Local connection failed: {e}", "ERROR")
        return False

def test_atlas_connection():
    """Test Atlas connection."""
    log("🌐 Testing Atlas connection...")
    try:
        client = MongoClient(ATLAS_URI, serverSelectionTimeoutMS=10000)
        db = client.get_default_database()
        
        # Test connection
        start_time = time.time()
        client.admin.command('ping')
        connection_time = (time.time() - start_time) * 1000
        
        log(f"✅ Atlas connected successfully!")
        log(f"📊 Connection time: {connection_time:.2f}ms")
        log(f"🗄️ Database name: {db.name}")
        
        client.close()
        return True
    except Exception as e:
        log(f"❌ Atlas connection failed: {e}", "ERROR")
        return False

def export_local_database():
    """Export local database using mongodump."""
    log("\n📦 EXPORTING LOCAL DATABASE")
    log("=" * 60)
    
    backup_dir = f"./atlas_migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    try:
        # Create backup directory
        os.makedirs(backup_dir, exist_ok=True)
        log(f"📁 Created backup directory: {backup_dir}")
        
        # Run mongodump
        cmd = [
            'mongodump',
            '--uri', LOCAL_URI,
            '--out', backup_dir
        ]
        
        log(f"🔄 Running: {' '.join(cmd[:2])} --uri=<LOCAL_URI> --out={backup_dir}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            log("✅ Database export completed successfully")
            
            # Verify backup
            db_backup_dir = os.path.join(backup_dir, 'sisarasa')
            if os.path.exists(db_backup_dir):
                files = os.listdir(db_backup_dir)
                log(f"📊 Exported {len(files)//2} collections")
                return backup_dir
            else:
                log("❌ Backup directory not found", "ERROR")
                return None
        else:
            log(f"❌ Export failed: {result.stderr}", "ERROR")
            return None
            
    except Exception as e:
        log(f"❌ Export error: {e}", "ERROR")
        return None

def import_to_atlas(backup_dir):
    """Import data to Atlas using mongorestore."""
    log("\n📤 IMPORTING TO ATLAS")
    log("=" * 60)
    
    try:
        db_backup_dir = os.path.join(backup_dir, 'sisarasa')
        
        cmd = [
            'mongorestore',
            '--uri', ATLAS_URI,
            '--drop',  # Drop existing collections
            db_backup_dir
        ]
        
        log(f"🔄 Running: mongorestore --uri=<ATLAS_URI> --drop {db_backup_dir}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            log("✅ Database import completed successfully")
            return True
        else:
            log(f"❌ Import failed: {result.stderr}", "ERROR")
            return False
            
    except Exception as e:
        log(f"❌ Import error: {e}", "ERROR")
        return False

def validate_migration():
    """Validate migration by comparing data counts."""
    log("\n✅ VALIDATING MIGRATION")
    log("=" * 60)
    
    try:
        # Connect to both databases
        local_client = MongoClient(LOCAL_URI, serverSelectionTimeoutMS=5000)
        atlas_client = MongoClient(ATLAS_URI, serverSelectionTimeoutMS=10000)
        
        local_db = local_client.get_default_database()
        atlas_db = atlas_client.get_default_database()
        
        # Get collections from local (excluding backups)
        local_collections = [c for c in local_db.list_collection_names() 
                           if 'backup' not in c.lower()]
        
        validation_passed = True
        
        for collection_name in local_collections:
            try:
                local_count = local_db[collection_name].count_documents({})
                atlas_count = atlas_db[collection_name].count_documents({}) if collection_name in atlas_db.list_collection_names() else 0
                
                if local_count == atlas_count:
                    log(f"✅ {collection_name}: {atlas_count:,} documents (matches)")
                else:
                    log(f"❌ {collection_name}: Local={local_count:,}, Atlas={atlas_count:,}", "ERROR")
                    validation_passed = False
                    
            except Exception as e:
                log(f"❌ Error validating {collection_name}: {e}", "ERROR")
                validation_passed = False
        
        local_client.close()
        atlas_client.close()
        
        return validation_passed
        
    except Exception as e:
        log(f"❌ Validation failed: {e}", "ERROR")
        return False

def update_env_file():
    """Update .env file with Atlas connection string."""
    log("\n⚙️ UPDATING .ENV FILE")
    log("=" * 60)
    
    try:
        # Backup current .env
        if os.path.exists('.env'):
            import shutil
            shutil.copy('.env', '.env.backup')
            log("📦 Created .env backup")
        
        # Read current .env
        env_lines = []
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_lines = f.readlines()
        
        # Update MONGO_URI
        updated = False
        for i, line in enumerate(env_lines):
            if line.startswith('MONGO_URI='):
                env_lines[i] = f"MONGO_URI={ATLAS_URI}\n"
                updated = True
                break
        
        if not updated:
            env_lines.append(f"MONGO_URI={ATLAS_URI}\n")
        
        # Write updated .env
        with open('.env', 'w') as f:
            f.writelines(env_lines)
        
        log("✅ Updated .env file with Atlas connection string")
        return True
        
    except Exception as e:
        log(f"❌ Failed to update .env file: {e}", "ERROR")
        return False

def main():
    """Execute the migration."""
    log("🚀 SISARASA MONGODB ATLAS MIGRATION")
    log("=" * 60)
    log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Step 1: Test local connection
    if not test_local_connection():
        log("❌ Cannot connect to local MongoDB. Please ensure it's running.", "ERROR")
        return False
    
    # Step 2: Test Atlas connection
    if not test_atlas_connection():
        log("❌ Cannot connect to Atlas. Please check your connection string.", "ERROR")
        return False
    
    # Step 3: Export local database
    backup_dir = export_local_database()
    if not backup_dir:
        log("❌ Failed to export local database.", "ERROR")
        return False
    
    # Step 4: Import to Atlas
    if not import_to_atlas(backup_dir):
        log("❌ Failed to import to Atlas.", "ERROR")
        return False
    
    # Step 5: Validate migration
    if not validate_migration():
        log("❌ Migration validation failed. Please check manually.", "ERROR")
        return False
    
    # Step 6: Update .env file
    if not update_env_file():
        log("⚠️ Failed to update .env file. Please update manually.", "WARNING")
    
    log("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
    log("=" * 60)
    log("✅ Your SisaRasa database is now running on MongoDB Atlas!")
    log("✅ Your application will now use the Atlas connection.")
    log("✅ Test your application to ensure everything works correctly.")
    
    return True

if __name__ == "__main__":
    print("🌟 SisaRasa MongoDB Atlas Migration")
    print("=" * 60)
    print("This will migrate your local MongoDB to Atlas.")
    print(f"Atlas Cluster: sisarasa.pzkt0dj.mongodb.net")
    print(f"Database User: farahfiqh")
    
    proceed = input("\nProceed with migration? (y/N): ").lower().strip()
    if proceed != 'y':
        print("❌ Migration cancelled by user")
        sys.exit(0)
    
    success = main()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("Your SisaRasa application is now connected to MongoDB Atlas.")
    else:
        print("\n❌ Migration failed. Check the logs above for details.")
        print("Your local database remains unchanged.")
