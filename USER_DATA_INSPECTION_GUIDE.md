# User Data Inspection Guide for Sisa Rasa

This guide provides multiple ways to inspect and debug user data in your Sisa Rasa recipe recommendation system.

## 🚀 Quick Start

### 1. API Endpoints (Easiest)

**List all users:**
```bash
curl http://localhost:5000/api/dev/users
```

**Get complete user data:**
```bash
# By email
curl http://localhost:5000/api/dev/user/<EMAIL>

# By user ID
curl http://localhost:5000/api/dev/user/USER_ID_HERE
```

### 2. Command Line Script

**List all users:**
```bash
python inspect_users.py list
```

**View specific user:**
```bash
python inspect_users.<NAME_EMAIL>
python inspect_users.py user USER_ID_HERE
```

**Export user data:**
```bash
python inspect_users.<NAME_EMAIL>
python inspect_users.<NAME_EMAIL> custom_filename.json
```

**System statistics:**
```bash
python inspect_users.py stats
```

### 3. Python Interactive Session

```python
# Import debug utilities
from src.api.models.debug_utils import *

# Quick functions
list_users()                    # Show all users
show_user("<EMAIL>")   # Show user details
show_stats()                    # Show system stats

# Detailed functions
users = get_all_users_summary()
user_data = inspect_user("<EMAIL>")
reviews = get_user_reviews_detailed("<EMAIL>")
stats = get_system_stats()
```

## 📊 What Data You Can Access

### Basic User Information
- User ID, name, email
- Account creation and update dates
- Admin status
- Profile image status

### User Activity
- **Saved Recipes**: All recipes saved by the user with details
- **Reviews & Ratings**: All reviews submitted with ratings, text, vote counts
- **Recipe Verifications**: Recipes the user has verified as working
- **Review Votes**: Helpful/unhelpful votes on other users' reviews

### Analytics & Behavior
- **Search History**: Recent searches and ingredient history
- **Usage Statistics**: Recipe views, saves, reviews given
- **Preferences**: Favorite ingredients, dietary restrictions
- **Cooking Streaks**: Current and longest cooking streaks

### System Data
- **Password Hash**: For debugging authentication issues
- **Dashboard Data**: Search statistics and history
- **Profile Images**: Base64 encoded image data

## 🔧 Debugging Common Issues

### Rating System Problems

**Check if user has submitted reviews:**
```python
reviews = get_user_reviews_detailed("<EMAIL>")
print(f"User has {reviews['total_reviews']} reviews")
```

**Find recent review activity:**
```bash
python inspect_users.<NAME_EMAIL>
# Look at the "Reviews" section for recent activity
```

**Check for duplicate reviews:**
```javascript
// In MongoDB shell
db.recipe_reviews.aggregate([
  {$group: {
    _id: {user_id: "$user_id", recipe_id: "$recipe_id"}, 
    count: {$sum: 1}
  }},
  {$match: {count: {$gt: 1}}}
])
```

### Authentication Issues

**Check password hash:**
```bash
curl http://localhost:5000/api/dev/user/<EMAIL> | jq '.user.basic_info.password_hash'
```

**Verify user exists:**
```python
user_data = inspect_user("<EMAIL>")
if 'error' in user_data:
    print("User not found")
else:
    print(f"User found: {user_data['basic_info']['name']}")
```

### Data Inconsistencies

**Check analytics vs actual data:**
```python
user_data = inspect_user("<EMAIL>")
analytics_reviews = user_data['analytics'].get('total_reviews_given', 0)
actual_reviews = user_data['activity_summary']['reviews_count']
print(f"Analytics: {analytics_reviews}, Actual: {actual_reviews}")
```

**Find users with high activity:**
```python
active_users = find_users_by_activity(min_reviews=5, min_saves=10)
for user in active_users:
    print(f"{user['name']}: {user['total_reviews']} reviews, {user['total_saves']} saves")
```

## 📁 Data Export Options

### JSON Export
```bash
# Export to auto-generated filename
python inspect_users.<NAME_EMAIL>

# Export to specific file
python inspect_users.<NAME_EMAIL> user_backup.json
```

### Python Export
```python
# Get JSON string
json_data = export_user_data_json("<EMAIL>")
with open("user_data.json", "w") as f:
    f.write(json_data)
```

### MongoDB Export
```bash
# Export user collection
mongoexport --db sisarasa --collection users --out users_backup.json

# Export specific user
mongoexport --db sisarasa --collection users --query '{"email":"<EMAIL>"}' --out user_backup.json
```

## 🔍 Advanced Queries

### Find Users by Criteria
```python
# Users with many reviews
active_reviewers = find_users_by_activity(min_reviews=10)

# Users with many saved recipes
recipe_savers = find_users_by_activity(min_saves=20)

# Users with both high activity
power_users = find_users_by_activity(min_reviews=5, min_saves=10)
```

### MongoDB Aggregation Examples
```javascript
// Top recipe savers
db.saved_recipes.aggregate([
  {$group: {_id: "$user_id", count: {$sum: 1}}},
  {$sort: {count: -1}},
  {$limit: 10}
])

// Users by registration date
db.users.aggregate([
  {$group: {
    _id: {$dateToString: {format: "%Y-%m", date: "$created_at"}},
    count: {$sum: 1}
  }},
  {$sort: {_id: 1}}
])
```

## 🛡️ Security Notes

### Development vs Production

**⚠️ IMPORTANT**: The `/api/dev/` endpoints expose sensitive data including password hashes. These should be:

1. **Removed in production** or protected with authentication
2. **Only used in development** environments
3. **Never exposed** to public networks

### Safe Production Debugging

For production environments, consider:

1. **VPN-only access** to debug endpoints
2. **Admin authentication** required for data access
3. **Audit logging** for all data access
4. **Data masking** for sensitive fields

## 📋 Quick Reference Commands

```bash
# List all users
curl http://localhost:5000/api/dev/users | jq '.users[] | {name, email, total_reviews, total_saves}'

# Get user by email
curl http://localhost:5000/api/dev/user/<EMAIL> | jq '.user.basic_info'

# Check user's recent reviews
curl http://localhost:5000/api/dev/user/<EMAIL> | jq '.user.reviews.reviews[0:3]'

# Export user data
python inspect_users.<NAME_EMAIL>

# System overview
python inspect_users.py stats

# MongoDB quick check
mongosh "mongodb://localhost:27017/sisarasa" --eval "db.users.countDocuments({})"
```

## 🆘 Troubleshooting

### Connection Issues
```bash
# Test MongoDB connection
mongosh "mongodb://localhost:27017/sisarasa" --eval "db.runCommand('ping')"

# Test API connection
curl http://localhost:5000/api/health
```

### Script Issues
```bash
# Check Python dependencies
pip install pymongo python-dotenv

# Verify environment variables
echo $MONGO_URI
```

### Data Issues
```python
# Check for missing data
user_data = inspect_user("<EMAIL>")
if not user_data.get('reviews'):
    print("No reviews found for user")

# Verify data consistency
stats = get_system_stats()
print(f"Total users: {stats['collections']['users']}")
```

This comprehensive toolkit should give you everything you need to inspect, debug, and understand your user data!
