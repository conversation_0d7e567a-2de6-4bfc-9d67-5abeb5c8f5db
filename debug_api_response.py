#!/usr/bin/env python3
"""
Debug script to examine the actual API response from shared recipes endpoint.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def create_test_user_and_login():
    """Create a test user and login to get a token."""
    print("Creating test user and logging in...")
    
    import time
    timestamp = str(int(time.time()))
    test_user = {
        "name": "Debug Test User",
        "email": f"debug{timestamp}@example.com",
        "password": "testpassword123"
    }
    
    try:
        # Try to register the user
        register_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
        print(f"Register response: {register_response.status_code}")
        
        # Try to login
        login_data = {
            "email": test_user["email"],
            "password": test_user["password"]
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"Login response: {login_response.status_code}")
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result.get('token')
            user_info = result.get('user', {})
            print(f"✓ Successfully logged in")
            print(f"User ID: {user_info.get('id')}")
            print(f"User Name: {user_info.get('name')}")
            print(f"User Email: {user_info.get('email')}")
            return token, user_info.get('id')
        else:
            print(f"✗ Login failed: {login_response.text}")
            return None, None
            
    except Exception as e:
        print(f"✗ Error during authentication: {e}")
        return None, None

def debug_shared_recipes_response(token):
    """Debug the shared recipes API response."""
    print("\n=== Debugging Shared Recipes API Response ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Number of recipes returned: {len(data)}")
            
            if data:
                print("\n=== First Recipe Data Structure ===")
                first_recipe = data[0]
                
                # Print all fields in the recipe
                for key, value in first_recipe.items():
                    print(f"{key}: {value}")
                
                print("\n=== Key Fields Analysis ===")
                print(f"user_id: '{first_recipe.get('user_id', 'MISSING')}'")
                print(f"user_name: '{first_recipe.get('user_name', 'MISSING')}'")
                print(f"username: '{first_recipe.get('username', 'MISSING')}'")
                print(f"submitted_by: '{first_recipe.get('submitted_by', 'MISSING')}'")
                print(f"prep_time: {first_recipe.get('prep_time', 'MISSING')}")
                print(f"cook_time: {first_recipe.get('cook_time', 'MISSING')}")
                print(f"servings: {first_recipe.get('servings', 'MISSING')}")
                print(f"difficulty: '{first_recipe.get('difficulty', 'MISSING')}'")
                
                return True
            else:
                print("No recipes found in response")
                return True
        else:
            print(f"✗ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error calling API: {e}")
        return False

def debug_current_user_endpoint(token):
    """Debug the current user endpoint."""
    print("\n=== Debugging Current User Endpoint ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Current user data:")
            print(json.dumps(data, indent=2))
            return data.get('user', {}).get('id')
        else:
            print(f"✗ API call failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error calling API: {e}")
        return None

def main():
    """Run debug analysis."""
    print("=== Community Recipe API Debug Analysis ===\n")
    
    # Get authentication token
    token, user_id = create_test_user_and_login()
    if not token:
        print("✗ Could not authenticate, stopping debug")
        return 1
    
    # Debug current user endpoint
    current_user_id = debug_current_user_endpoint(token)
    
    # Debug shared recipes response
    recipes_success = debug_shared_recipes_response(token)
    
    print(f"\n=== Debug Summary ===")
    print(f"Authentication: {'✓' if token else '✗'}")
    print(f"Current User ID: {current_user_id}")
    print(f"Shared Recipes API: {'✓' if recipes_success else '✗'}")
    
    return 0 if recipes_success else 1

if __name__ == "__main__":
    sys.exit(main())
