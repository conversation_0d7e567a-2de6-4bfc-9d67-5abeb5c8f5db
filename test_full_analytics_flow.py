#!/usr/bin/env python3
"""
Test the complete analytics flow: create user, perform actions, check analytics.
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_full_analytics_flow():
    """Test the complete analytics flow."""
    print("🧪 Testing Complete Analytics Flow")
    print("=" * 60)
    
    # Step 1: Create test user
    print("\n1️⃣ Creating test user...")
    test_user = {
        "name": "Full Analytics Test User",
        "email": f"full_analytics_test_{int(time.time())}@example.com",
        "password": "testpass123"
    }
    
    signup_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
    if signup_response.status_code != 201:
        print(f"❌ Failed to create test user: {signup_response.text}")
        return
    
    print("✅ Test user created successfully")
    
    # Step 2: Login user
    print("\n2️⃣ Logging in user...")
    login_response = requests.post(f"{BASE_URL}/api/auth/login", json={
        "email": test_user["email"],
        "password": test_user["password"]
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.text}")
        return
    
    login_data = login_response.json()
    token = login_data.get('token')
    
    if not token:
        print(f"❌ No token received")
        return
    
    print("✅ Login successful")
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    # Step 3: Get a sample recipe
    print("\n3️⃣ Getting sample recipe...")
    recipe_response = requests.post(f"{BASE_URL}/api/recommend", 
                                  json={"ingredients": ["chicken", "rice"], "limit": 1})
    
    if recipe_response.status_code != 200:
        print(f"❌ Failed to get recipe: {recipe_response.text}")
        return
    
    recipe_data = recipe_response.json()
    if not recipe_data.get('recipes'):
        print("❌ No recipes found")
        return
    
    recipe = recipe_data['recipes'][0]
    recipe_id = recipe['id']
    recipe_name = recipe['name']
    
    print(f"✅ Got sample recipe: {recipe_name} (ID: {recipe_id})")
    
    # Step 4: Save the recipe
    print("\n4️⃣ Saving recipe...")
    save_response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/save", headers=headers)
    
    if save_response.status_code == 200:
        print("✅ Recipe saved successfully")
    else:
        print(f"❌ Failed to save recipe: {save_response.text}")
    
    # Step 5: Add a review
    print("\n5️⃣ Adding review...")
    review_data = {
        "rating": 5,
        "review_text": "This is an amazing recipe! Very easy to follow and delicious results."
    }
    
    review_response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/review", 
                                  json=review_data, headers=headers)
    
    if review_response.status_code == 200:
        print("✅ Review added successfully")
    else:
        print(f"❌ Failed to add review: {review_response.text}")
    
    # Step 6: Track some events manually
    print("\n6️⃣ Tracking additional events...")
    
    # Track search event
    search_event = {
        "event_type": "search",
        "event_data": {
            "ingredients": ["chicken", "rice", "garlic"],
            "ingredient_count": 3
        }
    }
    
    track_response = requests.post(f"{BASE_URL}/api/analytics/track", 
                                 json=search_event, headers=headers)
    
    if track_response.status_code == 200:
        print("✅ Search event tracked")
    else:
        print(f"❌ Failed to track search event: {track_response.text}")
    
    # Track recipe view event
    view_event = {
        "event_type": "recipe_view",
        "event_data": {
            "recipe_id": recipe_id,
            "recipe_name": recipe_name
        }
    }
    
    track_response = requests.post(f"{BASE_URL}/api/analytics/track", 
                                 json=view_event, headers=headers)
    
    if track_response.status_code == 200:
        print("✅ Recipe view event tracked")
    else:
        print(f"❌ Failed to track recipe view event: {track_response.text}")
    
    # Step 7: Check analytics
    print("\n7️⃣ Checking analytics...")
    analytics_response = requests.get(f"{BASE_URL}/api/analytics/personal", headers=headers)
    
    if analytics_response.status_code != 200:
        print(f"❌ Analytics API failed: {analytics_response.text}")
        return
    
    analytics_data = analytics_response.json()
    
    if analytics_data.get('status') != 'success':
        print(f"❌ Analytics API error: {analytics_data}")
        return
    
    print("✅ Analytics retrieved successfully")
    
    # Display analytics data
    analytics = analytics_data.get('analytics', {})
    personal_stats = analytics.get('personal_stats', {})
    
    print(f"\n📊 ANALYTICS RESULTS:")
    print(f"   🔍 Total Searches: {personal_stats.get('total_searches', 0)}")
    print(f"   👀 Recipe Views: {personal_stats.get('total_recipe_views', 0)}")
    print(f"   💾 Saved Recipes: {personal_stats.get('total_recipe_saves', 0)}")
    print(f"   ⭐ Reviews Given: {personal_stats.get('total_reviews_given', 0)}")
    print(f"   🥬 Unique Ingredients: {personal_stats.get('unique_ingredients_tried', 0)}")
    
    # Check if the numbers are correct
    expected_saves = 1
    expected_reviews = 1
    expected_views = 1
    
    actual_saves = personal_stats.get('total_recipe_saves', 0)
    actual_reviews = personal_stats.get('total_reviews_given', 0)
    actual_views = personal_stats.get('total_recipe_views', 0)
    
    print(f"\n✅ VALIDATION:")
    print(f"   💾 Saved Recipes: Expected {expected_saves}, Got {actual_saves} {'✅' if actual_saves >= expected_saves else '❌'}")
    print(f"   ⭐ Reviews Given: Expected {expected_reviews}, Got {actual_reviews} {'✅' if actual_reviews >= expected_reviews else '❌'}")
    print(f"   👀 Recipe Views: Expected {expected_views}, Got {actual_views} {'✅' if actual_views >= expected_views else '❌'}")
    
    # Step 8: Test dashboard data
    print(f"\n8️⃣ Checking dashboard data...")
    dashboard_response = requests.get(f"{BASE_URL}/api/dashboard/data", headers=headers)
    
    if dashboard_response.status_code == 200:
        dashboard_data = dashboard_response.json()
        if dashboard_data.get('status') == 'success':
            data = dashboard_data.get('data', {})
            search_stats = data.get('search_stats', {})
            print(f"✅ Dashboard data retrieved")
            print(f"   📈 Recent Searches: {len(data.get('recent_searches', []))}")
            print(f"   🥬 Ingredient History: {len(data.get('ingredient_history', []))}")
            print(f"   🔍 Total Searches: {search_stats.get('total_searches', 0)}")
        else:
            print(f"❌ Dashboard API error: {dashboard_data}")
    else:
        print(f"❌ Dashboard API failed: {dashboard_response.text}")
    
    print(f"\n🎉 Analytics flow test completed!")
    
    if actual_saves >= expected_saves and actual_reviews >= expected_reviews:
        print("✅ ALL ANALYTICS WORKING CORRECTLY!")
        return True
    else:
        print("❌ Some analytics not working properly")
        return False

if __name__ == "__main__":
    success = test_full_analytics_flow()
    if success:
        print("\n🎯 The analytics dashboard should now show correct data in the frontend!")
    else:
        print("\n⚠️  There may still be issues with analytics tracking.")
