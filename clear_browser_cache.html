<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Browser Cache - SisaRasa</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2rem;
        }
        
        .explanation {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
            text-align: left;
        }
        
        .cache-info {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: left;
        }
        
        .warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: left;
        }
        
        .success {
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: left;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .cache-items {
            text-align: left;
            margin: 20px 0;
        }
        
        .cache-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .cache-size {
            color: #666;
            font-size: 0.9rem;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🧹</div>
        <h1>Clear Browser Cache & Data</h1>
        
        <div class="explanation">
            <p><strong>Issue Detected:</strong> Your browser may be showing cached data instead of the latest information from the database.</p>
            <p>This tool will help you clear all cached data to ensure you see the most up-to-date information from your MongoDB Atlas database.</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ What will be cleared:</strong>
            <ul>
                <li>Search results and recipe data</li>
                <li>User session data (you'll need to log in again)</li>
                <li>Saved preferences and recent searches</li>
                <li>Browser cache and temporary files</li>
            </ul>
        </div>
        
        <div class="cache-info">
            <strong>📊 Current Cache Status:</strong>
            <div class="cache-items" id="cacheItems">
                <div class="cache-item">
                    <span>localStorage Data</span>
                    <span class="cache-size" id="localStorageSize">Calculating...</span>
                </div>
                <div class="cache-item">
                    <span>sessionStorage Data</span>
                    <span class="cache-size" id="sessionStorageSize">Calculating...</span>
                </div>
                <div class="cache-item">
                    <span>Cached Recipes</span>
                    <span class="cache-size" id="cachedRecipes">Calculating...</span>
                </div>
                <div class="cache-item">
                    <span>User Session</span>
                    <span class="cache-size" id="userSession">Calculating...</span>
                </div>
            </div>
        </div>
        
        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div id="actionButtons">
            <button class="btn" onclick="analyzeCacheData()">🔍 Analyze Cache</button>
            <button class="btn btn-danger" onclick="clearAllCache()">🧹 Clear All Cache</button>
            <button class="btn btn-success" onclick="refreshAndRedirect()">🔄 Refresh & Continue</button>
        </div>
        
        <div id="results" style="display: none;"></div>
    </div>

    <script>
        let cacheAnalysis = {};
        
        function calculateStorageSize(storage) {
            let total = 0;
            for (let key in storage) {
                if (storage.hasOwnProperty(key)) {
                    total += storage[key].length + key.length;
                }
            }
            return total;
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function analyzeCacheData() {
            // Analyze localStorage
            const localStorageSize = calculateStorageSize(localStorage);
            document.getElementById('localStorageSize').textContent = formatBytes(localStorageSize);
            
            // Analyze sessionStorage
            const sessionStorageSize = calculateStorageSize(sessionStorage);
            document.getElementById('sessionStorageSize').textContent = formatBytes(sessionStorageSize);
            
            // Check for cached recipes
            const cachedRecipes = localStorage.getItem('lastSearchResults');
            const recipesSize = cachedRecipes ? cachedRecipes.length : 0;
            document.getElementById('cachedRecipes').textContent = recipesSize > 0 ? formatBytes(recipesSize) + ' (Found cached data!)' : 'No cached recipes';
            
            // Check user session
            const userToken = localStorage.getItem('token');
            const userName = localStorage.getItem('userName');
            const sessionInfo = userToken ? `Logged in as ${userName || 'Unknown'}` : 'Not logged in';
            document.getElementById('userSession').textContent = sessionInfo;
            
            cacheAnalysis = {
                localStorage: localStorageSize,
                sessionStorage: sessionStorageSize,
                cachedRecipes: recipesSize,
                hasUserSession: !!userToken
            };
            
            // Show warning if significant cache found
            if (localStorageSize > 1024 || recipesSize > 0) {
                showWarning('⚠️ Significant cached data found! This may be causing the synchronization issue.');
            }
        }
        
        function showWarning(message) {
            const warning = document.createElement('div');
            warning.className = 'warning';
            warning.innerHTML = `<strong>${message}</strong>`;
            document.getElementById('results').appendChild(warning);
            document.getElementById('results').style.display = 'block';
        }
        
        function showSuccess(message) {
            const success = document.createElement('div');
            success.className = 'success';
            success.innerHTML = `<strong>✅ ${message}</strong>`;
            document.getElementById('results').appendChild(success);
            document.getElementById('results').style.display = 'block';
        }
        
        function updateProgress(percent) {
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        async function clearAllCache() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('results').style.display = 'block';
            
            const steps = [
                { name: 'Clearing localStorage...', action: () => localStorage.clear() },
                { name: 'Clearing sessionStorage...', action: () => sessionStorage.clear() },
                { name: 'Clearing browser cache...', action: () => {
                    // Force reload without cache
                    if ('caches' in window) {
                        caches.keys().then(names => {
                            names.forEach(name => {
                                caches.delete(name);
                            });
                        });
                    }
                }},
                { name: 'Clearing cookies...', action: () => {
                    // Clear cookies for this domain
                    document.cookie.split(";").forEach(function(c) { 
                        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                    });
                }}
            ];
            
            for (let i = 0; i < steps.length; i++) {
                const step = steps[i];
                updateProgress((i / steps.length) * 100);
                
                const stepDiv = document.createElement('div');
                stepDiv.textContent = step.name;
                document.getElementById('results').appendChild(stepDiv);
                
                try {
                    await new Promise(resolve => setTimeout(resolve, 500)); // Visual delay
                    step.action();
                    stepDiv.textContent += ' ✅';
                } catch (error) {
                    stepDiv.textContent += ' ❌ Error: ' + error.message;
                }
            }
            
            updateProgress(100);
            showSuccess('All cache data cleared successfully!');
            
            // Update the action buttons
            document.getElementById('actionButtons').innerHTML = `
                <button class="btn btn-success" onclick="refreshAndRedirect()">🔄 Refresh Application</button>
                <button class="btn" onclick="location.reload()">🔄 Reload This Page</button>
            `;
        }
        
        function refreshAndRedirect() {
            showSuccess('Redirecting to application with fresh data...');
            setTimeout(() => {
                // Force a hard refresh and redirect
                window.location.href = '/dashboard?nocache=' + Date.now();
            }, 1000);
        }
        
        // Auto-analyze on page load
        window.onload = function() {
            analyzeCacheData();
        };
    </script>
</body>
</html>
