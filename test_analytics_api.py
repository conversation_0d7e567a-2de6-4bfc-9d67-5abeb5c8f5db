#!/usr/bin/env python3
"""
Test the analytics API to verify the data is being returned correctly.
"""

import requests
import json

BASE_URL = "http://127.0.0.1:5000"

def test_analytics_api():
    """Test the analytics API with existing users."""
    print("🧪 Testing Analytics API")
    print("=" * 50)

    # Create a test user first
    print("👤 Creating test user...")
    test_user = {
        "name": "Analytics Test User",
        "email": f"analytics_test_{int(__import__('time').time())}@example.com",
        "password": "testpass123"
    }

    signup_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
    if signup_response.status_code == 201:
        print("✅ Test user created successfully")
        test_users = [test_user]
    else:
        print(f"❌ Failed to create test user: {signup_response.text}")
        # Fallback to existing users with different passwords
        test_users = [
            {"email": "<EMAIL>", "password": "password"},
            {"email": "<EMAIL>", "password": "password"},
            {"email": "<EMAIL>", "password": "password"}
        ]

    for i, user_creds in enumerate(test_users, 1):
        print(f"\n{i}. Testing user: {user_creds['email']}")

        # Login user
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=user_creds)

        if login_response.status_code != 200:
            print(f"   ❌ Login failed: {login_response.text}")
            continue

        login_data = login_response.json()
        token = login_data.get('token')

        if not token:
            print(f"   ❌ No token received")
            continue

        print(f"   ✅ Login successful")

        # Test analytics API
        headers = {"Authorization": f"Bearer {token}"}
        analytics_response = requests.get(f"{BASE_URL}/api/analytics/personal", headers=headers)

        if analytics_response.status_code != 200:
            print(f"   ❌ Analytics API failed: {analytics_response.text}")
            continue

        analytics_data = analytics_response.json()

        if analytics_data.get('status') != 'success':
            print(f"   ❌ Analytics API error: {analytics_data}")
            continue

        print(f"   ✅ Analytics API successful")

        # Display analytics data
        analytics = analytics_data.get('analytics', {})
        personal_stats = analytics.get('personal_stats', {})

        print(f"   📊 Personal Stats:")
        print(f"      - Total Searches: {personal_stats.get('total_searches', 0)}")
        print(f"      - Recipe Views: {personal_stats.get('total_recipe_views', 0)}")
        print(f"      - Saved Recipes: {personal_stats.get('total_recipe_saves', 0)}")
        print(f"      - Reviews Given: {personal_stats.get('total_reviews_given', 0)}")
        print(f"      - Unique Ingredients: {personal_stats.get('unique_ingredients_tried', 0)}")

        # Test dashboard data API
        dashboard_response = requests.get(f"{BASE_URL}/api/dashboard/data", headers=headers)

        if dashboard_response.status_code == 200:
            dashboard_data = dashboard_response.json()
            if dashboard_data.get('status') == 'success':
                data = dashboard_data.get('data', {})
                search_stats = data.get('search_stats', {})
                print(f"   🎯 Dashboard Data:")
                print(f"      - Recent Searches: {len(data.get('recent_searches', []))}")
                print(f"      - Ingredient History: {len(data.get('ingredient_history', []))}")
                print(f"      - Search Stats Total: {search_stats.get('total_searches', 0)}")

        print(f"   🔍 Raw Analytics Response:")
        print(f"      {json.dumps(analytics, indent=2)[:200]}...")

if __name__ == "__main__":
    test_analytics_api()
