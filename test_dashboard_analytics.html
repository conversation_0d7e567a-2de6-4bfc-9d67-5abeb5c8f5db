<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Analytics</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">
</head>
<body>
    <div id="app" class="container mt-4">
        <h2>Dashboard Analytics Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Analytics Data Test</h5>
                    </div>
                    <div class="card-body">
                        <button @click="testAnalyticsAPI" class="btn btn-primary">Test Analytics API</button>
                        <div v-if="loading" class="mt-3">
                            <div class="spinner-border" role="status"></div>
                            <span class="ms-2">Loading...</span>
                        </div>
                        <div v-if="analyticsData" class="mt-3">
                            <h6>Popular Recipes ({{ (analyticsData.popular_recipes || []).length }}):</h6>
                            <ul class="list-group">
                                <li v-for="recipe in (analyticsData.popular_recipes || [])" :key="recipe.id" class="list-group-item">
                                    <strong>{{ recipe.name }}</strong>
                                    <br>
                                    <small>Rating: {{ recipe.avg_rating || recipe.rating || 'N/A' }} | Reviews: {{ recipe.review_count || 0 }}</small>
                                </li>
                            </ul>
                        </div>
                        <div v-if="error" class="alert alert-danger mt-3">
                            {{ error }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Rating Display Test</h5>
                    </div>
                    <div class="card-body">
                        <div v-for="rating in testRatings" :key="rating" class="mb-3">
                            <div class="d-flex align-items-center">
                                <span class="me-3">Rating {{ rating }}:</span>
                                <div class="rating-stars">
                                    <span v-for="star in 5" :key="star" 
                                          class="star" 
                                          :class="{ 'filled': star <= Math.round(rating) }"
                                          style="color: #ddd; font-size: 1rem;">★</span>
                                </div>
                                <span class="ms-2">{{ rating.toFixed(1) }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    loading: false,
                    analyticsData: null,
                    error: null,
                    testRatings: [4.8, 4.6, 4.2, 3.9, 3.5, 2.1]
                }
            },
            methods: {
                async testAnalyticsAPI() {
                    this.loading = true;
                    this.error = null;
                    this.analyticsData = null;
                    
                    try {
                        console.log('🔄 Testing analytics API...');
                        const response = await fetch('/api/analytics/prescriptive');
                        console.log('📊 Response status:', response.status);
                        
                        const data = await response.json();
                        console.log('📊 Response data:', data);
                        
                        if (data.status === 'success') {
                            this.analyticsData = data.data;
                            console.log('✅ Analytics data loaded successfully');
                        } else {
                            this.error = `API Error: ${data.message || 'Unknown error'}`;
                        }
                    } catch (error) {
                        console.error('❌ Error:', error);
                        this.error = `Network Error: ${error.message}`;
                    } finally {
                        this.loading = false;
                    }
                }
            },
            mounted() {
                // Auto-test on load
                this.testAnalyticsAPI();
            }
        }).mount('#app');
    </script>
    
    <style>
        .star {
            color: #ddd !important;
            transition: color 0.2s;
        }
        .star.filled {
            color: #ffc107 !important;
        }
    </style>
</body>
</html>
