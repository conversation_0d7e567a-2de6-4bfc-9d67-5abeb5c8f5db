#!/usr/bin/env python3
"""
Check analytics data in the database to debug the dashboard issue.
"""

import pymongo
from pprint import pprint

def check_analytics():
    """Check analytics data in the database."""
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🔍 Checking Analytics Data")
    print("=" * 50)
    
    # Check users and their analytics
    users = list(db.users.find({}, {'name': 1, 'email': 1, 'analytics': 1, 'dashboard_data': 1}))
    
    print(f"\n👥 Found {len(users)} users")
    
    for i, user in enumerate(users[:5], 1):  # Show first 5 users
        print(f"\n{i}. User: {user['name']} ({user['email']})")
        
        analytics = user.get('analytics', {})
        dashboard_data = user.get('dashboard_data', {})
        
        print(f"   📊 Analytics:")
        print(f"      - Recipe Views: {analytics.get('total_recipe_views', 0)}")
        print(f"      - Recipe Saves: {analytics.get('total_recipe_saves', 0)}")
        print(f"      - Reviews Given: {analytics.get('total_reviews_given', 0)}")
        print(f"      - Unique Ingredients: {analytics.get('discovery_stats', {}).get('unique_ingredients_tried', 0)}")
        
        print(f"   🎯 Dashboard Data:")
        search_stats = dashboard_data.get('search_stats', {})
        print(f"      - Total Searches: {search_stats.get('total_searches', 0)}")
        print(f"      - Most Used Ingredients: {len(search_stats.get('most_used_ingredients', {}))}")
    
    # Check saved recipes
    print(f"\n💾 Saved Recipes:")
    saved_recipes_count = db.recipes.count_documents({})
    print(f"   - Total recipes in DB: {saved_recipes_count}")
    
    # Check reviews
    print(f"\n⭐ Reviews:")
    reviews_count = db.recipe_reviews.count_documents({})
    print(f"   - Total reviews: {reviews_count}")
    
    # Check recent reviews
    recent_reviews = list(db.recipe_reviews.find({}, {'user_name': 1, 'recipe_id': 1, 'rating': 1, 'created_at': 1}).sort('created_at', -1).limit(5))
    print(f"   - Recent reviews:")
    for review in recent_reviews:
        print(f"     * {review['user_name']}: {review['rating']}⭐ for recipe {review['recipe_id']}")
    
    # Check verifications
    print(f"\n✅ Verifications:")
    verifications_count = db.recipe_verifications.count_documents({})
    print(f"   - Total verifications: {verifications_count}")

if __name__ == "__main__":
    check_analytics()
