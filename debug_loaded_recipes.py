#!/usr/bin/env python3
"""
Debug script to check what recipes are actually loaded in the recommender.
"""

import sys
import os
sys.path.append('src')

from api.app import app, recommender

def debug_loaded_recipes():
    """Debug what recipes are loaded in the recommender."""
    
    with app.app_context():
        print(f"Total recipes loaded: {len(recommender.recipes)}")
        print(f"Total ingredients: {len(recommender.knn_recommender.ingredient_names)}")
        
        # Find user recipes
        user_recipes = [r for r in recommender.recipes if r.get('is_user_recipe', False)]
        system_recipes = [r for r in recommender.recipes if not r.get('is_user_recipe', False)]
        
        print(f"\nSystem recipes: {len(system_recipes)}")
        print(f"User recipes: {len(user_recipes)}")
        
        if user_recipes:
            print(f"\n📋 User recipes loaded in recommender:")
            for i, recipe in enumerate(user_recipes, 1):
                print(f"{i}. {recipe.get('name', 'Unknown')}")
                print(f"   ID: {recipe.get('id', 'N/A')}")
                print(f"   Ingredients: {recipe.get('ingredients', [])}")
                print(f"   Submitted by: {recipe.get('submitted_by', 'N/A')}")
                print()
        else:
            print("\n❌ No user recipes found in recommender!")
            print("This suggests the loading process failed.")
        
        # Check some ingredient mappings
        print(f"\n🔍 Sample ingredient mappings:")
        sample_ingredients = list(recommender.knn_recommender.ingredient_names)[:10]
        for ingredient in sample_ingredients:
            recipe_indices = recommender.knn_recommender.ingredient_to_recipes.get(ingredient, [])
            print(f"   '{ingredient}' -> {len(recipe_indices)} recipes")

if __name__ == "__main__":
    print("Debugging Loaded Recipes...")
    print("=" * 50)
    debug_loaded_recipes()
