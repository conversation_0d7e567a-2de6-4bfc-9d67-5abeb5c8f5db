#!/usr/bin/env python3
"""
Test script to validate the Most Popular Recipes fixes.
This script tests the backend API and frontend functionality.
"""

import requests
import json
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:5000"
DASHBOARD_URL = f"{API_BASE_URL}/dashboard"

def test_prescriptive_analytics_api():
    """Test the /api/analytics/prescriptive endpoint."""
    print("🧪 Testing Prescriptive Analytics API...")
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/analytics/prescriptive", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'success':
                popular_recipes = data.get('data', {}).get('popular_recipes', [])
                
                print(f"✅ API Response: SUCCESS")
                print(f"📊 Popular recipes found: {len(popular_recipes)}")
                
                if popular_recipes:
                    print("\n📋 Popular Recipes Data Structure:")
                    for i, recipe in enumerate(popular_recipes[:2], 1):
                        print(f"\n  Recipe {i}: {recipe.get('name', 'Unknown')}")
                        print(f"    - ID: {recipe.get('id')}")
                        print(f"    - Rating: {recipe.get('rating', 'N/A')}")
                        print(f"    - Reviews: {recipe.get('review_count', 0)}")
                        print(f"    - Ingredients: {len(recipe.get('ingredients', []))}")
                        print(f"    - Latest Review: {'Yes' if recipe.get('latest_review') else 'No'}")
                        print(f"    - Description: {recipe.get('description', 'N/A')[:50]}...")
                
                return True
            else:
                print(f"❌ API Error: {data.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text[:200]}...")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False

def test_recipe_details_api():
    """Test the /api/recipe/{id} endpoint with a sample recipe."""
    print("\n🧪 Testing Recipe Details API...")
    
    # First get a recipe ID from the popular recipes
    try:
        response = requests.get(f"{API_BASE_URL}/api/analytics/prescriptive", timeout=10)
        if response.status_code == 200:
            data = response.json()
            popular_recipes = data.get('data', {}).get('popular_recipes', [])
            
            if popular_recipes:
                recipe_id = popular_recipes[0].get('id')
                print(f"🔍 Testing with recipe ID: {recipe_id}")
                
                # Test recipe details endpoint
                recipe_response = requests.get(f"{API_BASE_URL}/api/recipe/{recipe_id}", timeout=10)
                
                if recipe_response.status_code == 200:
                    recipe_data = recipe_response.json()
                    if recipe_data.get('status') == 'ok':
                        recipe = recipe_data.get('recipe', {})
                        print(f"✅ Recipe Details: SUCCESS")
                        print(f"📋 Recipe: {recipe.get('name')}")
                        print(f"    - Ingredients: {len(recipe.get('ingredients', []))}")
                        print(f"    - Steps: {len(recipe.get('steps', []))}")
                        print(f"    - Rating Data: {'Yes' if recipe.get('rating_data') else 'No'}")
                        return True
                    else:
                        print(f"❌ Recipe API Error: {recipe_data.get('message')}")
                        return False
                else:
                    print(f"❌ Recipe HTTP Error: {recipe_response.status_code}")
                    return False
            else:
                print("⚠️  No popular recipes found to test with")
                return False
                
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False

def test_dashboard_accessibility():
    """Test if the dashboard page loads correctly."""
    print("\n🧪 Testing Dashboard Page Accessibility...")
    
    try:
        response = requests.get(DASHBOARD_URL, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key elements
            checks = [
                ("Most Popular Recipes section", "Most Popular Recipes" in content),
                ("Recipe card styling", "recipe-card" in content),
                ("Vue.js integration", "handlePopularRecipeClick" in content),
                ("Analytics loading", "loadingAnalytics" in content),
                ("SweetAlert integration", "Swal.fire" in content)
            ]
            
            print("✅ Dashboard Page: ACCESSIBLE")
            print("📋 Content Checks:")
            
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"    {status} {check_name}")
                if not passed:
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ Dashboard HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection Error: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Most Popular Recipes Fix Validation")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run tests
    tests = [
        ("Prescriptive Analytics API", test_prescriptive_analytics_api),
        ("Recipe Details API", test_recipe_details_api),
        ("Dashboard Page Accessibility", test_dashboard_accessibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    elapsed = time.time() - start_time
    print(f"⏱️  Total time: {elapsed:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All tests passed! The Most Popular Recipes fix is working correctly.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
