#!/usr/bin/env python3
"""
Test script to verify the community recipe fixes.
This script tests:
1. Recipe metadata display (prep_time, cook_time, servings, difficulty)
2. Delete functionality for recipe owners
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def test_shared_recipes_endpoint():
    """Test the shared recipes endpoint to check if metadata is included."""
    print("Testing shared recipes endpoint...")
    
    # First, we need to login to get a token
    # For testing purposes, let's try to access the endpoint without auth first
    try:
        response = requests.get(f"{BASE_URL}/api/shared-recipes")
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 401:
            print("✓ Endpoint requires authentication (expected)")
            return True
        elif response.status_code == 200:
            data = response.json()
            print(f"✓ Got response with {len(data)} recipes")
            
            # Check if any recipe has the metadata fields
            if data:
                first_recipe = data[0]
                required_fields = ['prep_time', 'cook_time', 'servings', 'difficulty']
                missing_fields = [field for field in required_fields if field not in first_recipe]
                
                if missing_fields:
                    print(f"✗ Missing metadata fields: {missing_fields}")
                    print(f"Available fields: {list(first_recipe.keys())}")
                    return False
                else:
                    print("✓ All metadata fields present in recipe response")
                    print(f"Sample recipe metadata:")
                    for field in required_fields:
                        print(f"  {field}: {first_recipe[field]}")
                    return True
            else:
                print("✓ No recipes found (empty response)")
                return True
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to server. Make sure it's running on localhost:5000")
        return False
    except Exception as e:
        print(f"✗ Error testing endpoint: {e}")
        return False

def test_delete_endpoint():
    """Test that the delete endpoint exists."""
    print("\nTesting delete endpoint...")
    
    try:
        # Test with a dummy recipe ID - should return 401 (unauthorized) or 404 (not found)
        response = requests.delete(f"{BASE_URL}/api/recipe/dummy_id")
        print(f"Delete endpoint status: {response.status_code}")
        
        if response.status_code in [401, 404]:
            print("✓ Delete endpoint exists and requires authentication")
            return True
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to server")
        return False
    except Exception as e:
        print(f"✗ Error testing delete endpoint: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Community Recipe Fixes Test ===\n")
    
    tests = [
        test_shared_recipes_endpoint,
        test_delete_endpoint
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
