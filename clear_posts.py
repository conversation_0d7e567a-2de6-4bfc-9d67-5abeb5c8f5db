#!/usr/bin/env python3
"""
Clear Community Posts Script

This script clears all existing community posts, comments, and likes
to start with a clean slate for testing.
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient

# Add the project root to the path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        # Try to get MongoDB URI from environment
        mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongodb_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongodb_uri}")
        return db, client
        
    except Exception as e:
        print(f"❌ Error connecting to MongoDB: {e}")
        return None, None

def clear_community_posts(db):
    """Clear all community posts and related data."""
    try:
        print("\n🧹 Clearing community posts...")
        
        # Get collections
        posts_collection = db['community_posts']
        comments_collection = db['post_comments']
        likes_collection = db['post_likes']
        comment_likes_collection = db['comment_likes']
        
        # Count existing data
        posts_count = posts_collection.count_documents({})
        comments_count = comments_collection.count_documents({})
        likes_count = likes_collection.count_documents({})
        comment_likes_count = comment_likes_collection.count_documents({})
        
        print(f"  📊 Found {posts_count} posts, {comments_count} comments, {likes_count} post likes, {comment_likes_count} comment likes")
        
        if posts_count == 0 and comments_count == 0 and likes_count == 0 and comment_likes_count == 0:
            print("  ✅ No community data to clear")
            return
        
        # Create backup before clearing
        backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if posts_count > 0:
            backup_posts = list(posts_collection.find())
            backup_collection = db[f'community_posts_backup_{backup_timestamp}']
            backup_collection.insert_many(backup_posts)
            print(f"  📦 Backed up {len(backup_posts)} posts to community_posts_backup_{backup_timestamp}")
        
        if comments_count > 0:
            backup_comments = list(comments_collection.find())
            backup_collection = db[f'post_comments_backup_{backup_timestamp}']
            backup_collection.insert_many(backup_comments)
            print(f"  📦 Backed up {len(backup_comments)} comments to post_comments_backup_{backup_timestamp}")
        
        # Clear all collections
        posts_collection.delete_many({})
        comments_collection.delete_many({})
        likes_collection.delete_many({})
        comment_likes_collection.delete_many({})
        
        print(f"  🗑️  Cleared {posts_count} posts")
        print(f"  🗑️  Cleared {comments_count} comments")
        print(f"  🗑️  Cleared {likes_count} post likes")
        print(f"  🗑️  Cleared {comment_likes_count} comment likes")
        
        print("✅ Community posts cleared successfully!")
        
    except Exception as e:
        print(f"❌ Error clearing community posts: {e}")
        return False
    
    return True

def main():
    """Main function."""
    print("🧹 SisaRasa Community Posts Cleanup")
    print("=" * 50)
    print(f"Cleanup started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Clear community posts
        success = clear_community_posts(db)
        
        if success:
            print("\n✅ Community posts cleanup completed successfully!")
        else:
            print("\n❌ Community posts cleanup failed!")
            
    except Exception as e:
        print(f"\n❌ Unexpected error during cleanup: {e}")
    finally:
        if client:
            client.close()
            print("🔌 Database connection closed")

if __name__ == "__main__":
    main()
