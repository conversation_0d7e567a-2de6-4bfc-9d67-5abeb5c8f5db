#!/usr/bin/env python3
"""
Find Recipes with Reviews - Show specific recipes that users can test in the UI
"""

import pymongo

def find_recipes_with_reviews():
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🔍 RECIPES WITH REVIEWS - READY TO TEST IN UI")
    print("=" * 60)
    
    # Get recipes with the most reviews (using a simpler approach)
    from collections import Counter

    # Count reviews per recipe
    all_reviews = list(db.recipe_reviews.find({}, {'recipe_id': 1, 'rating': 1}))
    recipe_review_counts = Counter(review['recipe_id'] for review in all_reviews)

    # Get top 10 most reviewed recipes
    top_recipe_ids = [recipe_id for recipe_id, count in recipe_review_counts.most_common(10)]

    top_reviewed = []
    for recipe_id in top_recipe_ids:
        reviews = list(db.recipe_reviews.find({'recipe_id': recipe_id}, {'rating': 1}))
        avg_rating = sum(r['rating'] for r in reviews) / len(reviews) if reviews else 0
        top_reviewed.append({
            '_id': recipe_id,
            'review_count': len(reviews),
            'avg_rating': avg_rating
        })
    
    print("🏆 TOP 10 RECIPES WITH MOST REVIEWS:")
    print("-" * 60)
    
    for i, recipe_data in enumerate(top_reviewed[:5], 1):  # Show top 5
        recipe_id = recipe_data['_id']
        review_count = recipe_data['review_count']
        avg_rating = recipe_data['avg_rating']
        
        # Get recipe details
        recipe = db.recipes.find_one({'original_id': recipe_id})
        if recipe:
            recipe_name = recipe.get('name', 'Unknown Recipe')
            ingredients = recipe.get('ingredients', [])
            
            print(f"{i:2d}. {recipe_name}")
            print(f"    Recipe ID: {recipe_id}")
            print(f"    Reviews: {review_count} | Average Rating: {avg_rating:.1f}/5")
            
            # Show some ingredients for search testing
            if ingredients:
                search_ingredients = ingredients[:3]  # First 3 ingredients
                print(f"    Search with: {', '.join(search_ingredients)}")
            
            # Show sample review
            sample_review = db.recipe_reviews.find_one({'recipe_id': recipe_id})
            if sample_review:
                reviewer = sample_review.get('user_name', 'Anonymous')
                rating = sample_review.get('rating', 0)
                text = sample_review.get('review_text', '')[:50]
                print(f"    Sample Review: {reviewer} - {rating}/5 - \"{text}...\"")
            
            print()
    
    print("\n🧪 HOW TO TEST IN THE UI:")
    print("-" * 60)
    print("1. Start your SisaRasa application")
    print("2. Go to the dashboard and search for ingredients from any recipe above")
    print("3. Look for the recipe in the search results")
    print("4. You should see the review count (e.g., '8 reviews') on the recipe card")
    print("5. Click 'View Reviews' to see all reviews for that recipe")
    print("6. The reviews should display with user names, ratings, and review text")
    
    print(f"\n📊 SUMMARY:")
    print(f"   Total recipes in database: {db.recipes.count_documents({})}")
    print(f"   Total reviews: {db.recipe_reviews.count_documents({})}")
    print(f"   Recipes with reviews: {len(db.recipe_reviews.distinct('recipe_id'))}")
    print(f"   Average reviews per recipe: {db.recipe_reviews.count_documents({}) / len(db.recipe_reviews.distinct('recipe_id')):.1f}")
    
    client.close()

if __name__ == "__main__":
    find_recipes_with_reviews()
