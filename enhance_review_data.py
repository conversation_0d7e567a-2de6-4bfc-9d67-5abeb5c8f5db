#!/usr/bin/env python3
"""
Enhanced Review Data Generator for SisaRasa

This script regenerates review data with more realistic and comprehensive reviews
while keeping existing users and other data intact.
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from generate_synthetic_data import SyntheticDataGenerator, USER_PERSONAS
    import pymongo
    import random
    
    def clear_existing_reviews():
        """Clear existing review and vote data."""
        print("🧹 Clearing existing review data...")
        
        client = pymongo.MongoClient('mongodb://localhost:27017/')
        db = client.sisarasa
        
        # Clear reviews and votes
        reviews_deleted = db.recipe_reviews.delete_many({}).deleted_count
        votes_deleted = db.review_votes.delete_many({}).deleted_count
        
        print(f"   Deleted {reviews_deleted} reviews")
        print(f"   Deleted {votes_deleted} votes")
        
        # Reset user review counts in analytics
        db.users.update_many(
            {},
            {'$set': {'analytics.total_reviews_given': 0}}
        )
        
        client.close()
        return reviews_deleted, votes_deleted
    
    def enhance_reviews_only():
        """Generate enhanced reviews without affecting other data."""
        print("🌟 Generating Enhanced Review Data")
        print("=" * 50)
        
        # Clear existing reviews first
        clear_existing_reviews()
        
        # Create generator and load existing data
        generator = SyntheticDataGenerator()
        generator.load_recipes()
        
        # Load existing users from database and add missing persona field
        existing_users = list(generator.db.users.find({}))

        # Add persona field to users who don't have it
        personas = list(USER_PERSONAS.keys())
        for user in existing_users:
            if 'persona' not in user:
                user['persona'] = random.choice(personas)

        generator.users = existing_users
        
        print(f"📚 Using {len(generator.recipes)} recipes")
        print(f"👥 Using {len(generator.users)} existing users")
        
        # Generate only reviews and community interactions
        generator.generate_reviews()
        generator.generate_community_interactions()
        
        # Update user analytics for review counts
        generator.update_user_analytics()
        
        print("\n" + "=" * 50)
        print("🎉 ENHANCED REVIEW DATA COMPLETE!")
        print("=" * 50)
        print(f"⭐ New reviews generated: {generator.generated_data['reviews']}")
        print(f"👍 Community interactions: {generator.generated_data['interactions']}")
        print("\n✨ Your reviews are now more realistic and comprehensive!")
        
        generator.client.close()
    
    def main():
        """Main function."""
        print("SisaRasa Enhanced Review Data Generator")
        print("=" * 45)
        print("This will replace existing reviews with enhanced, more realistic ones.")
        print("User accounts and other data will remain unchanged.")
        
        # Ask for confirmation
        response = input("\n⚠️  This will replace all existing reviews. Continue? (y/N): ")
        if response.lower() != 'y':
            print("Operation cancelled.")
            return
        
        enhance_reviews_only()
    
    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("\nMake sure you have the required dependencies installed:")
    print("pip install pymongo bcrypt numpy")
    print("\nAlso ensure MongoDB is running on localhost:27017")
except Exception as e:
    print(f"❌ Error: {e}")
    print("\nPlease check your MongoDB connection and try again.")
