#!/usr/bin/env python3
"""
Test script to verify profile display fixes in recipe sharing functionality.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def test_login():
    """Test login and get auth token"""
    print("Testing login...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            print(f"✅ Login successful for user: {data['user']['name']}")
            return data['token']
        else:
            print(f"❌ Login failed: {data.get('message')}")
            return None
    else:
        print(f"❌ Login request failed with status {response.status_code}")
        return None

def test_recipe_owner_display(token):
    """Test recipe owner profile display in shared recipes"""
    print("\n🔍 Testing recipe owner profile display...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
    
    if response.status_code == 200:
        recipes = response.json()
        print(f"✅ Found {len(recipes)} shared recipes")
        
        if recipes:
            recipe = recipes[0]
            print(f"\n📋 Recipe: {recipe.get('name', 'N/A')}")
            
            # Check required fields for recipe owner display
            required_fields = ['user_name', 'username', 'user_profile_image', 'profile_picture']
            missing_fields = []
            
            for field in required_fields:
                if field not in recipe or recipe[field] is None:
                    missing_fields.append(field)
                else:
                    print(f"   ✅ {field}: {'[IMAGE DATA]' if field.endswith('image') or field.endswith('picture') else recipe[field]}")
            
            if missing_fields:
                print(f"   ❌ Missing fields: {missing_fields}")
                return False, None
            else:
                print("   ✅ All recipe owner profile fields present")
                return True, recipe.get('id')
        else:
            print("❌ No recipes found")
            return False, None
    else:
        print(f"❌ Shared recipes request failed with status {response.status_code}")
        return False, None

def test_recipe_comments_display(token, recipe_id):
    """Test comment author profile display in recipe comments"""
    print(f"\n🔍 Testing recipe comment author display for recipe {recipe_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # First, add a test comment
    comment_data = {"content": "Test comment for profile display verification"}
    response = requests.post(f"{BASE_URL}/api/community/recipe/{recipe_id}/comments", 
                           headers=headers, json=comment_data)
    
    if response.status_code == 200:
        print("✅ Test comment added successfully")
    else:
        print(f"⚠️  Could not add test comment (status {response.status_code})")
    
    # Now get comments to check profile display
    response = requests.get(f"{BASE_URL}/api/community/recipe/{recipe_id}/comments", headers=headers)
    
    if response.status_code == 200:
        comments = response.json()
        print(f"✅ Found {len(comments)} comments")
        
        if comments:
            comment = comments[0]
            print(f"\n💬 Comment: {comment.get('content', 'N/A')[:50]}...")
            
            # Check required fields for comment author display
            required_fields = ['user_name', 'user_profile_image']
            missing_fields = []
            
            for field in required_fields:
                if field not in comment:
                    missing_fields.append(field)
                else:
                    value = comment[field]
                    if field.endswith('image') and value:
                        print(f"   ✅ {field}: [IMAGE DATA]")
                    elif field.endswith('image') and not value:
                        print(f"   ✅ {field}: [NO IMAGE]")
                    else:
                        print(f"   ✅ {field}: {value}")
            
            if missing_fields:
                print(f"   ❌ Missing fields: {missing_fields}")
                return False
            else:
                print("   ✅ All comment author profile fields present")
                return True
        else:
            print("ℹ️  No comments found for this recipe")
            return True  # No comments is not an error
    else:
        print(f"❌ Recipe comments request failed with status {response.status_code}")
        return False

def test_community_posts_comments(token):
    """Test comment author display in community posts"""
    print(f"\n🔍 Testing community post comment author display...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get community posts
    response = requests.get(f"{BASE_URL}/api/community/posts", headers=headers)
    
    if response.status_code == 200:
        posts = response.json()
        print(f"✅ Found {len(posts)} community posts")
        
        if posts:
            post = posts[0]
            post_id = post['id']
            
            # Add a test comment to the first post
            comment_data = {"content": "Test comment for community post profile display"}
            response = requests.post(f"{BASE_URL}/api/community/posts/{post_id}/comments", 
                                   headers=headers, json=comment_data)
            
            if response.status_code == 200:
                print("✅ Test comment added to community post")
            else:
                print(f"⚠️  Could not add test comment to community post (status {response.status_code})")
            
            # Get comments for the post
            response = requests.get(f"{BASE_URL}/api/community/posts/{post_id}/comments", headers=headers)
            
            if response.status_code == 200:
                comments = response.json()
                print(f"✅ Found {len(comments)} comments on community post")
                
                if comments:
                    comment = comments[0]
                    print(f"\n💬 Community post comment: {comment.get('content', 'N/A')[:50]}...")
                    
                    # Check required fields
                    required_fields = ['user_name', 'user_profile_image']
                    missing_fields = []
                    
                    for field in required_fields:
                        if field not in comment:
                            missing_fields.append(field)
                        else:
                            value = comment[field]
                            if field.endswith('image') and value:
                                print(f"   ✅ {field}: [IMAGE DATA]")
                            elif field.endswith('image') and not value:
                                print(f"   ✅ {field}: [NO IMAGE]")
                            else:
                                print(f"   ✅ {field}: {value}")
                    
                    if missing_fields:
                        print(f"   ❌ Missing fields: {missing_fields}")
                        return False
                    else:
                        print("   ✅ All community post comment author fields present")
                        return True
                else:
                    print("ℹ️  No comments found on community posts")
                    return True
            else:
                print(f"❌ Community post comments request failed with status {response.status_code}")
                return False
        else:
            print("ℹ️  No community posts found")
            return True
    else:
        print(f"❌ Community posts request failed with status {response.status_code}")
        return False

def main():
    """Run all profile display tests"""
    print("🧪 Testing: Profile Display Fixes in Recipe Sharing")
    print("=" * 60)
    
    # Test 1: Login and get token
    token = test_login()
    if not token:
        print("\n❌ Cannot proceed without authentication token")
        sys.exit(1)
    
    # Test 2: Recipe owner profile display
    recipe_owner_ok, recipe_id = test_recipe_owner_display(token)
    
    # Test 3: Recipe comment author display
    recipe_comments_ok = False
    if recipe_id:
        recipe_comments_ok = test_recipe_comments_display(token, recipe_id)
    
    # Test 4: Community post comment author display
    community_comments_ok = test_community_posts_comments(token)
    
    # Summary
    print("\n" + "=" * 60)
    print("🎯 Test Results Summary:")
    print(f"   Recipe Owner Display: {'✅ PASS' if recipe_owner_ok else '❌ FAIL'}")
    print(f"   Recipe Comments Display: {'✅ PASS' if recipe_comments_ok else '❌ FAIL'}")
    print(f"   Community Comments Display: {'✅ PASS' if community_comments_ok else '❌ FAIL'}")
    
    if recipe_owner_ok and recipe_comments_ok and community_comments_ok:
        print("\n🎉 All profile display tests PASSED!")
        print("   Frontend templates should now correctly display user profile information.")
    else:
        print("\n⚠️  Some tests FAILED. Check the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
