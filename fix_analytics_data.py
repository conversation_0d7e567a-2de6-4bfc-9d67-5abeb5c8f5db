#!/usr/bin/env python3
"""
Fix analytics data by backfilling missing counts from actual database data.
"""

import pymongo
from bson.objectid import ObjectId
from datetime import datetime

def fix_analytics_data():
    """Fix analytics data by counting actual database records."""
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🔧 Fixing Analytics Data")
    print("=" * 50)
    
    # Get all users
    users = list(db.users.find({}))
    
    print(f"👥 Processing {len(users)} users...")
    
    for user in users:
        user_id = str(user['_id'])
        user_name = user['name']
        
        print(f"\n🔄 Processing user: {user_name}")
        
        # Get current analytics
        analytics = user.get('analytics', {})
        if not analytics:
            analytics = {
                'total_recipe_views': 0,
                'total_recipe_saves': 0,
                'total_reviews_given': 0,
                'cuisine_preferences': {},
                'cooking_streak': {
                    'current_streak': 0,
                    'longest_streak': 0,
                    'last_activity_date': None
                },
                'monthly_activity': {},
                'discovery_stats': {
                    'unique_ingredients_tried': 0,
                    'recipe_diversity_score': 0
                }
            }
        
        # Count actual saved recipes for this user
        saved_recipes_count = len(user.get('saved_recipes', []))
        
        # Count actual reviews given by this user
        reviews_count = db.recipe_reviews.count_documents({'user_id': user_id})
        
        # Count actual verifications by this user
        verifications_count = db.recipe_verifications.count_documents({'user_id': user_id})
        
        # Update analytics with actual counts
        old_saves = analytics.get('total_recipe_saves', 0)
        old_reviews = analytics.get('total_reviews_given', 0)
        
        analytics['total_recipe_saves'] = saved_recipes_count
        analytics['total_reviews_given'] = reviews_count
        
        # Update discovery stats based on dashboard data
        dashboard_data = user.get('dashboard_data', {})
        search_stats = dashboard_data.get('search_stats', {})
        most_used_ingredients = search_stats.get('most_used_ingredients', {})
        analytics['discovery_stats']['unique_ingredients_tried'] = len(most_used_ingredients)
        
        # Update user document
        result = db.users.update_one(
            {'_id': user['_id']},
            {
                '$set': {
                    'analytics': analytics,
                    'updated_at': datetime.utcnow()
                }
            }
        )
        
        print(f"   📊 Updated analytics:")
        print(f"      - Saved Recipes: {old_saves} → {saved_recipes_count}")
        print(f"      - Reviews Given: {old_reviews} → {reviews_count}")
        print(f"      - Verifications: {verifications_count}")
        print(f"      - Unique Ingredients: {len(most_used_ingredients)}")
        
        if result.modified_count > 0:
            print(f"   ✅ Analytics updated successfully")
        else:
            print(f"   ⚠️  No changes made")
    
    print(f"\n🎉 Analytics data fix completed!")
    
    # Show summary
    print(f"\n📈 Summary:")
    total_saves = sum([len(u.get('saved_recipes', [])) for u in users])
    total_reviews = db.recipe_reviews.count_documents({})
    total_verifications = db.recipe_verifications.count_documents({})
    
    print(f"   - Total Saved Recipes: {total_saves}")
    print(f"   - Total Reviews: {total_reviews}")
    print(f"   - Total Verifications: {total_verifications}")

if __name__ == "__main__":
    fix_analytics_data()
