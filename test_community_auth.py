#!/usr/bin/env python3
"""
Test script to verify community page authentication flow.
"""

import requests
import json

BASE_URL = "http://localhost:5000"

def test_login():
    """Test login with a known user."""
    print("🔐 Testing login...")
    
    # Try with test user
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"  # Common test password
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"   📡 Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            if token:
                print("✅ Login successful")
                print(f"   🔑 Token: {token[:50]}...")
                return token
            else:
                print("❌ No token in response")
                return None
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def test_auth_me(token):
    """Test the /api/auth/me endpoint."""
    print("\n👤 Testing /api/auth/me endpoint...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        print(f"   📡 Auth/me response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Auth endpoint working")
            print(f"   👤 User: {data.get('user', {}).get('name', 'Unknown')}")
            print(f"   📧 Email: {data.get('user', {}).get('email', 'Unknown')}")
            return True
        else:
            print(f"❌ Auth endpoint failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing auth endpoint: {e}")
        return False

def test_community_posts(token):
    """Test the community posts endpoint."""
    print("\n🏘️ Testing community posts endpoint...")
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(f"{BASE_URL}/api/community/posts", headers=headers)
        print(f"   📡 Community posts response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Community posts endpoint working")
            print(f"   📝 Posts count: {len(data) if isinstance(data, list) else 'Unknown'}")
            return True
        else:
            print(f"❌ Community posts failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error testing community posts: {e}")
        return False

def test_community_page(token):
    """Test accessing the community page directly."""
    print("\n🌐 Testing community page access...")
    
    try:
        response = requests.get(f"{BASE_URL}/community")
        print(f"   📡 Community page response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Community page loads successfully")
            # Check if the page contains expected content
            if "Community" in response.text and "Vue" in response.text:
                print("   ✅ Page contains expected content")
                return True
            else:
                print("   ⚠️ Page loaded but may be missing content")
                return False
        else:
            print(f"❌ Community page failed to load: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error accessing community page: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Community Page Authentication Flow")
    print("=" * 50)
    
    # Test login
    token = test_login()
    if not token:
        print("\n❌ Cannot proceed without valid token")
        return
    
    # Test auth endpoint
    auth_works = test_auth_me(token)
    if not auth_works:
        print("\n❌ Auth endpoint not working")
        return
    
    # Test community endpoints
    community_api_works = test_community_posts(token)
    community_page_works = test_community_page(token)
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   🔐 Login: {'✅ PASS' if token else '❌ FAIL'}")
    print(f"   👤 Auth endpoint: {'✅ PASS' if auth_works else '❌ FAIL'}")
    print(f"   🏘️ Community API: {'✅ PASS' if community_api_works else '❌ FAIL'}")
    print(f"   🌐 Community page: {'✅ PASS' if community_page_works else '❌ FAIL'}")
    
    if token and auth_works:
        print(f"\n🔑 Valid token for testing: {token}")
        print("\n💡 To test in browser:")
        print("   1. Open browser console on community page")
        print(f"   2. Run: localStorage.setItem('token', '{token}')")
        print("   3. Refresh the page")

if __name__ == "__main__":
    main()
