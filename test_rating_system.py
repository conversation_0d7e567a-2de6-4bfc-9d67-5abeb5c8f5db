#!/usr/bin/env python3
"""
Test script to diagnose rating submission issues.
Run this script to test the rating system and identify potential problems.
"""

import requests
import json
import time
import sys

# Configuration
BASE_URL = "http://127.0.0.1:5000"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "password123"

def test_api_health():
    """Test if the API is running."""
    print("🔍 Testing API Health...")
    try:
        response = requests.get(f"{BASE_URL}/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API is running")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API - make sure the server is running")
        return False
    except requests.exceptions.Timeout:
        print("❌ API request timed out")
        return False

def test_authentication():
    """Test user authentication."""
    print("\n🔐 Testing Authentication...")
    login_data = {
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data, timeout=10)
        if response.status_code == 200:
            data = response.json()
            token = data.get('token')
            if token:
                print("✅ Authentication successful")
                return token
            else:
                print("❌ No token in response")
                return None
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def get_test_recipe(token):
    """Get a recipe to test rating on."""
    print("\n📋 Getting Test Recipe...")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/recommend", 
            json={"ingredients": ["chicken", "rice"], "limit": 1},
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'ok' and data.get('recipes'):
                recipe = data['recipes'][0]
                recipe_id = recipe.get('id')
                recipe_name = recipe.get('name')
                print(f"✅ Got test recipe: {recipe_name} (ID: {recipe_id})")
                return recipe_id, recipe_name
            else:
                print("❌ No recipes returned")
                return None, None
        else:
            print(f"❌ Failed to get recipes: {response.status_code}")
            return None, None
    except Exception as e:
        print(f"❌ Error getting recipes: {e}")
        return None, None

def test_rating_submission(token, recipe_id, recipe_name):
    """Test rating submission with detailed logging."""
    print(f"\n⭐ Testing Rating Submission for '{recipe_name}'...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    rating_data = {
        "rating": 5,
        "review_text": "Test review from rating system test"
    }
    
    print(f"   Submitting rating: {rating_data}")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/api/recipe/{recipe_id}/review", 
            json=rating_data,
            headers=headers,
            timeout=30  # Longer timeout for rating submission
        )
        end_time = time.time()
        
        print(f"   Response time: {end_time - start_time:.2f} seconds")
        print(f"   Response status: {response.status_code}")
        print(f"   Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Rating submission successful!")
            print(f"   Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ Rating submission failed")
            print(f"   Response: {response.text}")
            
            # Try to parse error details
            try:
                error_data = response.json()
                print(f"   Error details: {json.dumps(error_data, indent=2)}")
            except:
                pass
            return False
                
    except requests.exceptions.Timeout:
        print("❌ Rating submission timed out")
        return False
    except Exception as e:
        print(f"❌ Error submitting rating: {e}")
        return False

def test_multiple_submissions(token, recipe_id, recipe_name):
    """Test multiple rapid submissions to check for race conditions."""
    print(f"\n🔄 Testing Multiple Rapid Submissions...")
    
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"   Attempt {i + 1}/{total_tests}...")
        
        # Vary the rating to test updates
        rating = (i % 5) + 1
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        rating_data = {
            "rating": rating,
            "review_text": f"Test review #{i + 1} - rating {rating}"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/recipe/{recipe_id}/review", 
                json=rating_data,
                headers=headers,
                timeout=15
            )
            
            if response.status_code == 200:
                success_count += 1
                print(f"   ✅ Attempt {i + 1} successful")
            else:
                print(f"   ❌ Attempt {i + 1} failed: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Attempt {i + 1} error: {e}")
        
        # Small delay between attempts
        time.sleep(0.5)
    
    print(f"\n📊 Multiple submission results: {success_count}/{total_tests} successful")
    return success_count == total_tests

def main():
    """Main test function."""
    print("🧪 Rating System Diagnostic Test")
    print("=" * 50)
    
    # Test 1: API Health
    if not test_api_health():
        print("\n❌ API is not accessible. Please start the server and try again.")
        sys.exit(1)
    
    # Test 2: Authentication
    token = test_authentication()
    if not token:
        print("\n❌ Authentication failed. Please check your credentials.")
        sys.exit(1)
    
    # Test 3: Get test recipe
    recipe_id, recipe_name = get_test_recipe(token)
    if not recipe_id:
        print("\n❌ Could not get test recipe.")
        sys.exit(1)
    
    # Test 4: Single rating submission
    if not test_rating_submission(token, recipe_id, recipe_name):
        print("\n❌ Single rating submission failed.")
        return False
    
    # Test 5: Multiple rapid submissions
    if not test_multiple_submissions(token, recipe_id, recipe_name):
        print("\n⚠️  Multiple submissions had some failures.")
    
    print("\n🎉 Rating system test completed!")
    print("\nIf you're still experiencing issues:")
    print("1. Check the server logs for detailed error messages")
    print("2. Verify MongoDB is running and accessible")
    print("3. Check your JWT token expiration settings")
    print("4. Monitor network connectivity")
    
    return True

if __name__ == "__main__":
    main()
