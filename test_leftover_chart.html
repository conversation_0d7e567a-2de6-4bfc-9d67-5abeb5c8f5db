<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Leftover Chart</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Leftover Analytics Test</h1>
        <div id="status" class="status loading">Loading analytics data...</div>
        
        <div class="chart-container">
            <canvas id="leftoverChart"></canvas>
        </div>
        
        <div id="data-display">
            <h3>Raw API Data:</h3>
            <pre id="raw-data"></pre>
        </div>
    </div>

    <script>
        let chart = null;
        
        async function testLeftoverAnalytics() {
            const statusDiv = document.getElementById('status');
            const rawDataDiv = document.getElementById('raw-data');
            
            try {
                console.log('🔄 Fetching leftover analytics...');
                statusDiv.textContent = 'Fetching data from API...';
                
                const response = await fetch('http://127.0.0.1:5001/api/analytics/leftover-ingredients');
                console.log('📡 Response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('📊 Data received:', data);
                
                // Display raw data
                rawDataDiv.textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ API working! Data loaded successfully.';
                    
                    // Create chart
                    createChart(data.data.most_searched_leftovers);
                } else {
                    throw new Error(data.message || 'API returned error status');
                }
                
            } catch (error) {
                console.error('❌ Error:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ Error: ${error.message}`;
                rawDataDiv.textContent = `Error: ${error.message}`;
            }
        }
        
        function createChart(data) {
            const canvas = document.getElementById('leftoverChart');
            const ctx = canvas.getContext('2d');
            
            // Destroy existing chart
            if (chart) {
                chart.destroy();
            }
            
            const colors = [
                '#ea5e18', // Orange
                '#fedf2f', // Yellow  
                '#083640', // Dark teal
                '#0a4550', // Medium teal
                '#6c757d'  // Gray
            ];
            
            chart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: data.map(item => item.name),
                    datasets: [{
                        data: data.map(item => item.percentage),
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                font: {
                                    family: 'Arial',
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const item = data[context.dataIndex];
                                    return `${item.name}: ${item.count} searches (${item.percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
            
            console.log('📈 Chart created successfully!');
        }
        
        // Start the test when page loads
        document.addEventListener('DOMContentLoaded', testLeftoverAnalytics);
    </script>
</body>
</html>
