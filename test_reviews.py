#!/usr/bin/env python3
"""
Test Reviews Script - Verify that reviews are working correctly
"""

import pymongo

def test_reviews():
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🧪 Testing Review System...")
    
    # Get matching recipe IDs
    review_recipe_ids = set(db.recipe_reviews.distinct('recipe_id'))
    db_recipe_ids = set(str(id) for id in db.recipes.distinct('original_id'))
    matches = list(db_recipe_ids & review_recipe_ids)
    
    print(f"📊 Statistics:")
    print(f"   Total recipes in database: {db.recipes.count_documents({})}")
    print(f"   Total reviews in database: {db.recipe_reviews.count_documents({})}")
    print(f"   Recipes with reviews: {len(matches)}")
    
    if matches:
        # Test first matching recipe
        test_id = matches[0]
        print(f"\n🧪 Testing recipe ID: {test_id}")
        
        # Get recipe
        recipe = db.recipes.find_one({'original_id': test_id})
        if recipe:
            recipe_name = recipe.get('name', 'Unknown')
            print(f"📖 Recipe: {recipe_name}")
            print(f"   MongoDB _id: {recipe['_id']}")
        
        # Get reviews
        reviews = list(db.recipe_reviews.find({'recipe_id': test_id}).limit(3))
        print(f"⭐ Reviews: {len(reviews)} found")
        
        for i, review in enumerate(reviews, 1):
            user_name = review.get('user_name')
            rating = review.get('rating')
            text = review.get('review_text', '')[:40]
            print(f"   {i}. {user_name} - {rating}/5 - {text}...")
        
        print(f"\n✅ SUCCESS: Recipe {test_id} has working reviews!")
        print(f"   This recipe should show '{len(reviews)} reviews' in the UI")
        print(f"   and clicking 'View Reviews' should display them.")
        
        # Test a few more recipes
        print(f"\n🔍 Testing more recipes:")
        for test_id in matches[1:6]:  # Test 5 more
            recipe = db.recipes.find_one({'original_id': test_id})
            review_count = db.recipe_reviews.count_documents({'recipe_id': test_id})
            if recipe:
                recipe_name = recipe.get('name', 'Unknown')[:40]
                print(f"   {test_id}: {recipe_name}... ({review_count} reviews)")
    else:
        print("❌ No matching recipes found!")
    
    client.close()

if __name__ == "__main__":
    test_reviews()
