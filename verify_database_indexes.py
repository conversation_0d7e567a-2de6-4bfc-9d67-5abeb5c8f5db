#!/usr/bin/env python3
"""
Database Index Verification Script for SisaRasa Community Features

This script verifies and creates necessary database indexes for optimal performance.
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient, ASCENDING, DESCENDING
from bson import ObjectId

# Add the project root to the path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        # Try to get MongoDB URI from environment or use default
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def verify_collection_indexes(db, collection_name, required_indexes):
    """Verify and create required indexes for a collection."""
    print(f"\n📊 Verifying indexes for collection: {collection_name}")
    
    collection = db[collection_name]
    existing_indexes = collection.list_indexes()
    existing_index_names = set()
    
    # Get existing index names
    for index in existing_indexes:
        existing_index_names.add(index['name'])
    
    print(f"  Existing indexes: {list(existing_index_names)}")
    
    # Check and create required indexes
    created_indexes = []
    for index_name, index_spec in required_indexes.items():
        if index_name not in existing_index_names:
            try:
                collection.create_index(index_spec, name=index_name)
                created_indexes.append(index_name)
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ❌ Failed to create index {index_name}: {e}")
        else:
            print(f"  ✓ Index exists: {index_name}")
    
    return created_indexes

def verify_community_posts_indexes(db):
    """Verify indexes for community_posts collection."""
    required_indexes = {
        'created_at_-1': [('created_at', DESCENDING)],
        'user_id_1_created_at_-1': [('user_id', ASCENDING), ('created_at', DESCENDING)],
        'is_active_1_created_at_-1': [('is_active', ASCENDING), ('created_at', DESCENDING)],
        'tags_1_created_at_-1': [('tags', ASCENDING), ('created_at', DESCENDING)],
        'user_id_1': [('user_id', ASCENDING)]
    }
    
    return verify_collection_indexes(db, 'community_posts', required_indexes)

def verify_post_comments_indexes(db):
    """Verify indexes for post_comments collection."""
    required_indexes = {
        'post_id_1_created_at_-1': [('post_id', ASCENDING), ('created_at', DESCENDING)],
        'user_id_1_created_at_-1': [('user_id', ASCENDING), ('created_at', DESCENDING)],
        'parent_comment_id_1_created_at_-1': [('parent_comment_id', ASCENDING), ('created_at', DESCENDING)],
        'is_active_1_created_at_-1': [('is_active', ASCENDING), ('created_at', DESCENDING)]
    }
    
    return verify_collection_indexes(db, 'post_comments', required_indexes)

def verify_post_likes_indexes(db):
    """Verify indexes for post_likes collection."""
    required_indexes = {
        'post_id_1_user_id_1': [('post_id', ASCENDING), ('user_id', ASCENDING)],
        'post_id_1_created_at_-1': [('post_id', ASCENDING), ('created_at', DESCENDING)],
        'user_id_1_created_at_-1': [('user_id', ASCENDING), ('created_at', DESCENDING)]
    }
    
    return verify_collection_indexes(db, 'post_likes', required_indexes)

def verify_comment_likes_indexes(db):
    """Verify indexes for comment_likes collection."""
    required_indexes = {
        'comment_id_1_user_id_1': [('comment_id', ASCENDING), ('user_id', ASCENDING)],
        'comment_id_1_created_at_-1': [('comment_id', ASCENDING), ('created_at', DESCENDING)],
        'user_id_1_created_at_-1': [('user_id', ASCENDING), ('created_at', DESCENDING)]
    }
    
    return verify_collection_indexes(db, 'comment_likes', required_indexes)

def verify_recipes_indexes(db):
    """Verify indexes for recipes collection."""
    required_indexes = {
        'submitted_by_1': [('submitted_by', ASCENDING)],
        'is_user_submitted_1_created_at_-1': [('is_user_submitted', ASCENDING), ('created_at', DESCENDING)],
        'approval_status_1_created_at_-1': [('approval_status', ASCENDING), ('created_at', DESCENDING)],
        'cuisine_1': [('cuisine', ASCENDING)],
        'difficulty_1': [('difficulty', ASCENDING)]
    }
    
    return verify_collection_indexes(db, 'recipes', required_indexes)

def verify_users_indexes(db):
    """Verify indexes for users collection."""
    required_indexes = {
        'email_1': [('email', ASCENDING)]
    }
    
    return verify_collection_indexes(db, 'users', required_indexes)

def verify_data_consistency(db):
    """Verify data consistency across collections."""
    print("\n🔍 Verifying data consistency...")
    
    issues = []
    
    # Check community posts have valid user references
    posts_collection = db['community_posts']
    users_collection = db['users']
    
    invalid_user_posts = 0
    for post in posts_collection.find():
        user_id = post.get('user_id')
        if user_id:
            try:
                user_exists = users_collection.find_one({'_id': ObjectId(user_id)})
                if not user_exists:
                    invalid_user_posts += 1
            except:
                invalid_user_posts += 1
    
    if invalid_user_posts > 0:
        issues.append(f"Found {invalid_user_posts} community posts with invalid user references")
    
    # Check post comments have valid post references
    comments_collection = db['post_comments']
    invalid_post_comments = 0
    for comment in comments_collection.find():
        post_id = comment.get('post_id')
        if post_id:
            try:
                post_exists = posts_collection.find_one({'_id': ObjectId(post_id)})
                if not post_exists:
                    invalid_post_comments += 1
            except:
                invalid_post_comments += 1
    
    if invalid_post_comments > 0:
        issues.append(f"Found {invalid_post_comments} comments with invalid post references")
    
    # Check post likes have valid references
    likes_collection = db['post_likes']
    invalid_post_likes = 0
    for like in likes_collection.find():
        post_id = like.get('post_id')
        user_id = like.get('user_id')
        
        if post_id:
            try:
                post_exists = posts_collection.find_one({'_id': ObjectId(post_id)})
                if not post_exists:
                    invalid_post_likes += 1
                    continue
            except:
                invalid_post_likes += 1
                continue
        
        if user_id:
            try:
                user_exists = users_collection.find_one({'_id': ObjectId(user_id)})
                if not user_exists:
                    invalid_post_likes += 1
            except:
                invalid_post_likes += 1
    
    if invalid_post_likes > 0:
        issues.append(f"Found {invalid_post_likes} post likes with invalid references")
    
    if issues:
        print("  ⚠️  Data consistency issues found:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print("  ✅ No data consistency issues found")
    
    return issues

def main():
    """Main verification function."""
    print("🔍 SisaRasa Database Index Verification")
    print("=" * 50)
    print(f"Verification started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Verify indexes for all collections
        all_created_indexes = []
        
        all_created_indexes.extend(verify_community_posts_indexes(db))
        all_created_indexes.extend(verify_post_comments_indexes(db))
        all_created_indexes.extend(verify_post_likes_indexes(db))
        all_created_indexes.extend(verify_comment_likes_indexes(db))
        all_created_indexes.extend(verify_recipes_indexes(db))
        all_created_indexes.extend(verify_users_indexes(db))
        
        # Verify data consistency
        consistency_issues = verify_data_consistency(db)
        
        # Summary
        print("\n📋 VERIFICATION SUMMARY")
        print("=" * 50)
        print(f"Total indexes created: {len(all_created_indexes)}")
        if all_created_indexes:
            print("Created indexes:")
            for index in all_created_indexes:
                print(f"  - {index}")
        
        print(f"Data consistency issues: {len(consistency_issues)}")
        
        print(f"\n✅ Verification completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    main()
