#!/usr/bin/env python3
"""
User Data Inspector for Sisa Rasa Recipe Recommendation System

This script allows developers to inspect user data directly from the command line.
Useful for debugging, troubleshooting, and understanding user behavior.

Usage:
    python inspect_users.py list                    # List all users
    python inspect_users.py user <email_or_id>      # Get detailed user info
    python inspect_users.py export <email_or_id>    # Export user data to JSON
    python inspect_users.py stats                   # Show system statistics
"""

import sys
import json
import pymongo
from datetime import datetime
from bson.objectid import ObjectId
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# MongoDB connection
MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017/sisarasa')

def connect_to_db():
    """Connect to MongoDB database."""
    try:
        client = pymongo.MongoClient(MONGO_URI)
        db = client.sisarasa
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {MONGO_URI}")
        return db
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        sys.exit(1)

def format_datetime(dt):
    """Format datetime for display."""
    if dt:
        return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
    return 'N/A'

def list_all_users(db):
    """List all users with basic information."""
    print("\n📋 All Users in System")
    print("=" * 80)
    
    users = list(db.users.find({}, {
        'name': 1, 
        'email': 1, 
        'created_at': 1,
        'analytics.total_reviews_given': 1,
        'analytics.total_recipe_saves': 1
    }).sort('created_at', -1))
    
    if not users:
        print("No users found in the system.")
        return
    
    print(f"{'ID':<25} {'Name':<20} {'Email':<30} {'Created':<20} {'Reviews':<8} {'Saves':<8}")
    print("-" * 120)
    
    for user in users:
        user_id = str(user['_id'])[:24]
        name = user.get('name', 'N/A')[:19]
        email = user.get('email', 'N/A')[:29]
        created = format_datetime(user.get('created_at'))[:19]
        reviews = user.get('analytics', {}).get('total_reviews_given', 0)
        saves = user.get('analytics', {}).get('total_recipe_saves', 0)
        
        print(f"{user_id:<25} {name:<20} {email:<30} {created:<20} {reviews:<8} {saves:<8}")
    
    print(f"\nTotal users: {len(users)}")

def get_user_details(db, user_identifier):
    """Get detailed information for a specific user."""
    print(f"\n🔍 User Details for: {user_identifier}")
    print("=" * 80)
    
    # Try to find user by email first, then by ID
    user = None
    if '@' in user_identifier:
        user = db.users.find_one({'email': user_identifier.lower()})
    else:
        try:
            if ObjectId.is_valid(user_identifier):
                user = db.users.find_one({'_id': ObjectId(user_identifier)})
        except:
            pass
    
    if not user:
        print(f"❌ User not found: {user_identifier}")
        return None
    
    user_id = str(user['_id'])
    
    # Get related data
    saved_recipes = list(db.saved_recipes.find({'user_id': user_id}))
    reviews = list(db.recipe_reviews.find({'user_id': user_id}).sort('created_at', -1))
    verifications = list(db.recipe_verifications.find({'user_id': user_id}).sort('created_at', -1))
    review_votes = list(db.review_votes.find({'user_id': user_id}).sort('created_at', -1))
    
    # Display basic info
    print(f"👤 Basic Information:")
    print(f"   ID: {user_id}")
    print(f"   Name: {user.get('name', 'N/A')}")
    print(f"   Email: {user.get('email', 'N/A')}")
    print(f"   Created: {format_datetime(user.get('created_at'))}")
    print(f"   Updated: {format_datetime(user.get('updated_at'))}")
    print(f"   Admin: {user.get('is_admin', False)}")
    print(f"   Profile Image: {'Yes' if user.get('profile_image') else 'No'}")
    
    # Display preferences
    preferences = user.get('preferences', {})
    print(f"\n⚙️  Preferences:")
    print(f"   Favorite Ingredients: {preferences.get('favorite_ingredients', [])}")
    print(f"   Dietary Restrictions: {preferences.get('dietary_restrictions', [])}")
    
    # Display analytics
    analytics = user.get('analytics', {})
    print(f"\n📊 Analytics:")
    print(f"   Recipe Views: {analytics.get('total_recipe_views', 0)}")
    print(f"   Recipe Saves: {analytics.get('total_recipe_saves', 0)}")
    print(f"   Reviews Given: {analytics.get('total_reviews_given', 0)}")
    print(f"   Cuisine Preferences: {analytics.get('cuisine_preferences', {})}")
    
    cooking_streak = analytics.get('cooking_streak', {})
    print(f"   Current Streak: {cooking_streak.get('current_streak', 0)}")
    print(f"   Longest Streak: {cooking_streak.get('longest_streak', 0)}")
    
    # Display dashboard data
    dashboard = user.get('dashboard_data', {})
    search_stats = dashboard.get('search_stats', {})
    print(f"\n🔍 Search Activity:")
    print(f"   Total Searches: {search_stats.get('total_searches', 0)}")
    print(f"   Last Search: {format_datetime(search_stats.get('last_search_date'))}")
    print(f"   Recent Searches: {len(dashboard.get('recent_searches', []))}")
    print(f"   Ingredient History: {len(dashboard.get('ingredient_history', []))}")
    
    # Display saved recipes
    print(f"\n💾 Saved Recipes ({len(saved_recipes)}):")
    for recipe in saved_recipes[:5]:  # Show first 5
        print(f"   - {recipe.get('name', 'N/A')} (saved: {format_datetime(recipe.get('created_at'))})")
    if len(saved_recipes) > 5:
        print(f"   ... and {len(saved_recipes) - 5} more")
    
    # Display reviews
    print(f"\n⭐ Reviews ({len(reviews)}):")
    for review in reviews[:5]:  # Show first 5
        rating = "⭐" * review.get('rating', 0)
        text = review.get('review_text', 'No text')[:50]
        print(f"   - Recipe {review.get('recipe_id', 'N/A')}: {rating} - {text}...")
        print(f"     Created: {format_datetime(review.get('created_at'))}")
    if len(reviews) > 5:
        print(f"   ... and {len(reviews) - 5} more")
    
    # Display verifications
    print(f"\n✅ Verifications ({len(verifications)}):")
    for verification in verifications[:3]:  # Show first 3
        notes = verification.get('notes', 'No notes')[:50]
        has_photo = 'with photo' if 'photo_data' in verification else 'no photo'
        print(f"   - Recipe {verification.get('recipe_id', 'N/A')}: {notes}... ({has_photo})")
        print(f"     Created: {format_datetime(verification.get('created_at'))}")
    if len(verifications) > 3:
        print(f"   ... and {len(verifications) - 3} more")
    
    # Display review votes
    print(f"\n👍 Review Votes ({len(review_votes)}):")
    helpful_votes = len([v for v in review_votes if v.get('vote_type') == 'helpful'])
    unhelpful_votes = len([v for v in review_votes if v.get('vote_type') == 'unhelpful'])
    print(f"   Helpful votes given: {helpful_votes}")
    print(f"   Unhelpful votes given: {unhelpful_votes}")
    
    return user

def export_user_data(db, user_identifier, filename=None):
    """Export user data to JSON file."""
    user = get_user_details(db, user_identifier)
    if not user:
        return
    
    user_id = str(user['_id'])
    
    # Get all related data
    saved_recipes = list(db.saved_recipes.find({'user_id': user_id}))
    reviews = list(db.recipe_reviews.find({'user_id': user_id}))
    verifications = list(db.recipe_verifications.find({'user_id': user_id}))
    review_votes = list(db.review_votes.find({'user_id': user_id}))
    
    # Convert ObjectIds to strings for JSON serialization
    def convert_objectids(obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, dict):
            return {k: convert_objectids(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_objectids(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return obj
    
    export_data = {
        'user': convert_objectids(user),
        'saved_recipes': convert_objectids(saved_recipes),
        'reviews': convert_objectids(reviews),
        'verifications': convert_objectids(verifications),
        'review_votes': convert_objectids(review_votes),
        'export_timestamp': datetime.utcnow().isoformat()
    }
    
    if not filename:
        safe_email = user.get('email', user_id).replace('@', '_').replace('.', '_')
        filename = f"user_data_{safe_email}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        print(f"\n💾 User data exported to: {filename}")
        print(f"   File size: {os.path.getsize(filename)} bytes")
    except Exception as e:
        print(f"❌ Failed to export data: {e}")

def show_system_stats(db):
    """Show overall system statistics."""
    print("\n📈 System Statistics")
    print("=" * 50)
    
    # Basic counts
    total_users = db.users.count_documents({})
    total_reviews = db.recipe_reviews.count_documents({})
    total_verifications = db.recipe_verifications.count_documents({})
    total_saved_recipes = db.saved_recipes.count_documents({})
    total_review_votes = db.review_votes.count_documents({})
    
    print(f"👥 Total Users: {total_users}")
    print(f"⭐ Total Reviews: {total_reviews}")
    print(f"✅ Total Verifications: {total_verifications}")
    print(f"💾 Total Saved Recipes: {total_saved_recipes}")
    print(f"👍 Total Review Votes: {total_review_votes}")
    
    # Recent activity (last 7 days)
    from datetime import timedelta
    seven_days_ago = datetime.utcnow() - timedelta(days=7)
    
    recent_users = db.users.count_documents({'created_at': {'$gte': seven_days_ago}})
    recent_reviews = db.recipe_reviews.count_documents({'created_at': {'$gte': seven_days_ago}})
    
    print(f"\n📅 Last 7 Days:")
    print(f"   New Users: {recent_users}")
    print(f"   New Reviews: {recent_reviews}")
    
    # Top reviewers
    print(f"\n🏆 Top Reviewers:")
    top_reviewers = list(db.recipe_reviews.aggregate([
        {'$group': {'_id': '$user_id', 'count': {'$sum': 1}, 'user_name': {'$first': '$user_name'}}},
        {'$sort': {'count': -1}},
        {'$limit': 5}
    ]))
    
    for i, reviewer in enumerate(top_reviewers, 1):
        print(f"   {i}. {reviewer['user_name']}: {reviewer['count']} reviews")

def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)
    
    command = sys.argv[1].lower()
    db = connect_to_db()
    
    if command == 'list':
        list_all_users(db)
    
    elif command == 'user':
        if len(sys.argv) < 3:
            print("❌ Please provide user email or ID")
            print("Usage: python inspect_users.py user <email_or_id>")
            sys.exit(1)
        user_identifier = sys.argv[2]
        get_user_details(db, user_identifier)
    
    elif command == 'export':
        if len(sys.argv) < 3:
            print("❌ Please provide user email or ID")
            print("Usage: python inspect_users.py export <email_or_id> [filename]")
            sys.exit(1)
        user_identifier = sys.argv[2]
        filename = sys.argv[3] if len(sys.argv) > 3 else None
        export_user_data(db, user_identifier, filename)
    
    elif command == 'stats':
        show_system_stats(db)
    
    else:
        print(f"❌ Unknown command: {command}")
        print(__doc__)
        sys.exit(1)

if __name__ == "__main__":
    main()
