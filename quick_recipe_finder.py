import pymongo
import json

client = pymongo.MongoClient('mongodb://localhost:27017/')
db = client.sisarasa

# Get recipes with reviews
reviews = list(db.recipe_reviews.find({}).limit(20))
recipe_ids = [review['recipe_id'] for review in reviews]

print("🔍 RECIPES WITH SYNTHETIC REVIEWS TO TEST:")
print("=" * 50)

# Load clean recipes
with open('data/clean_recipes.json', 'r', encoding='utf-8') as f:
    recipes = json.load(f)

count = 0
for recipe in recipes:
    if str(recipe['id']) in recipe_ids and count < 5:
        ingredients = recipe.get('ingredients', [])
        # Get first few meaningful ingredients
        search_ingredients = []
        for ing in ingredients:
            if len(ing) > 2 and ing.lower() not in ['salt', 'pepper', 'oil', 'water', 'sugar']:
                search_ingredients.append(ing)
            if len(search_ingredients) >= 3:
                break
        
        print(f"{count + 1}. Recipe: {recipe['name']}")
        print(f"   🔍 Search with: {', '.join(search_ingredients[:3])}")
        
        # Get review count for this recipe
        review_count = db.recipe_reviews.count_documents({'recipe_id': str(recipe['id'])})
        print(f"   ⭐ Has {review_count} reviews")
        print()
        count += 1

print("\n🎯 QUICK SEARCH SUGGESTIONS:")
print("Try these ingredient combinations:")
print("• chicken, rice, onion")
print("• beef, potato, carrot") 
print("• pasta, tomato, cheese")
print("• egg, bread, milk")
print("• salmon, lemon, garlic")

client.close()
