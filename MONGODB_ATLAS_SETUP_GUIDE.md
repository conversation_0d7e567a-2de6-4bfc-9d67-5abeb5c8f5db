# MongoDB Atlas Setup Guide for SisaRasa Migration

This comprehensive guide walks you through setting up MongoDB Atlas for your SisaRasa application migration.

## 📋 Prerequisites

Before starting, ensure you have:
- [ ] A valid email address for Atlas account
- [ ] Credit card for verification (Free tier available)
- [ ] Your current local MongoDB running
- [ ] MongoDB tools installed (`mongodump`, `mongorestore`)

## 🚀 Step 1: Create MongoDB Atlas Account

### 1.1 Sign Up
1. Go to [MongoDB Atlas](https://cloud.mongodb.com)
2. Click **"Try Free"**
3. Fill in your details:
   - First Name, Last Name
   - Email address
   - Password (strong password recommended)
4. Click **"Create your Atlas account"**
5. Verify your email address

### 1.2 Complete Profile
1. Select your goal: **"Learn MongoDB"** or **"Build a new application"**
2. Choose your experience level
3. Select preferred language: **Python**
4. Click **"Finish"**

## 🏗️ Step 2: Create Your First Cluster

### 2.1 Choose Deployment Type
1. Select **"Database"** from the left sidebar
2. Click **"Create"** button
3. Choose **"M0 Sandbox"** (Free tier - perfect for SisaRasa)

### 2.2 Configure Cluster
1. **Cloud Provider**: Choose **AWS** (recommended)
2. **Region**: Select closest to your location:
   - For Malaysia: **Singapore (ap-southeast-1)**
   - For US: **N. Virginia (us-east-1)**
   - For Europe: **Ireland (eu-west-1)**
3. **Cluster Name**: Enter `sisarasa-cluster` or your preferred name
4. Click **"Create Cluster"**

⏱️ **Wait Time**: Cluster creation takes 3-5 minutes

## 🔐 Step 3: Configure Security

### 3.1 Create Database User
1. Go to **"Database Access"** in left sidebar
2. Click **"Add New Database User"**
3. Choose **"Password"** authentication
4. Enter credentials:
   - **Username**: `sisarasa-admin`
   - **Password**: Generate secure password or create your own
   - **Save this password securely!**
5. **Database User Privileges**: Select **"Read and write to any database"**
6. Click **"Add User"**

### 3.2 Configure Network Access
1. Go to **"Network Access"** in left sidebar
2. Click **"Add IP Address"**
3. Choose one option:

#### Option A: Allow Access from Anywhere (Development)
- Click **"Allow Access from Anywhere"**
- IP Address: `0.0.0.0/0`
- ⚠️ **Warning**: Only for development/testing

#### Option B: Add Your Current IP (Recommended)
- Click **"Add Current IP Address"**
- Your IP will be auto-detected
- Add description: "Development Machine"

#### Option C: Add Specific IPs (Production)
- Enter specific IP addresses for your servers
- Add description for each IP

4. Click **"Confirm"**

## 🔗 Step 4: Get Connection String

### 4.1 Access Connection Details
1. Go to **"Database"** in left sidebar
2. Find your cluster and click **"Connect"**
3. Choose **"Connect your application"**

### 4.2 Configure Connection
1. **Driver**: Select **Python**
2. **Version**: Select **3.6 or later**
3. **Connection String**: Copy the provided string

Example connection string:
```
mongodb+srv://sisarasa-admin:<password>@sisarasa-cluster.xxxxx.mongodb.net/?retryWrites=true&w=majority
```

### 4.3 Customize Connection String
Replace `<password>` with your actual database user password:
```
mongodb+srv://sisarasa-admin:<EMAIL>/?retryWrites=true&w=majority
```

## 🗄️ Step 5: Prepare for Migration

### 5.1 Test Connection
Create a test script to verify connection:

```python
from pymongo import MongoClient

# Your Atlas connection string
atlas_uri = "mongodb+srv://sisarasa-admin:<EMAIL>/?retryWrites=true&w=majority"

try:
    client = MongoClient(atlas_uri)
    # Test connection
    client.admin.command('ping')
    print("✅ Successfully connected to Atlas!")
    
    # List databases
    databases = client.list_database_names()
    print(f"Available databases: {databases}")
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
finally:
    client.close()
```

### 5.2 Verify Local Database
Run the migration readiness check:
```bash
python database_migration_check.py
```

### 5.3 Clean Up Database (Optional but Recommended)
Remove backup collections to reduce migration time:
```bash
python pre_migration_cleanup.py
```

## 📊 Step 6: Atlas Dashboard Overview

### 6.1 Key Sections
- **Clusters**: View and manage your databases
- **Database Access**: Manage users and permissions
- **Network Access**: Configure IP whitelist
- **Monitoring**: View performance metrics
- **Backup**: Configure automated backups

### 6.2 Important Settings
1. **Data Explorer**: Browse your collections after migration
2. **Metrics**: Monitor database performance
3. **Profiler**: Analyze slow queries
4. **Alerts**: Set up monitoring alerts

## 🔧 Step 7: Atlas Configuration Best Practices

### 7.1 Security Best Practices
- [ ] Use strong passwords for database users
- [ ] Limit IP access to specific addresses
- [ ] Enable two-factor authentication on your Atlas account
- [ ] Regularly rotate database passwords
- [ ] Use least privilege principle for database users

### 7.2 Performance Optimization
- [ ] Choose region closest to your users
- [ ] Monitor connection pool settings
- [ ] Set up appropriate indexes
- [ ] Enable profiler for slow query analysis

### 7.3 Backup and Recovery
- [ ] Enable automated backups (available in paid tiers)
- [ ] Test restore procedures
- [ ] Document recovery processes

## 🚨 Troubleshooting Common Issues

### Connection Issues
**Problem**: "Authentication failed"
- **Solution**: Verify username/password in connection string
- **Check**: Database user exists and has correct permissions

**Problem**: "Connection timeout"
- **Solution**: Check network access settings
- **Verify**: Your IP address is whitelisted

**Problem**: "DNS resolution failed"
- **Solution**: Check internet connection
- **Verify**: Connection string format is correct

### Migration Issues
**Problem**: "Collection already exists"
- **Solution**: Use `--drop` flag with mongorestore
- **Alternative**: Delete collections manually in Atlas

**Problem**: "Slow migration"
- **Solution**: Check network speed
- **Consider**: Migrating during off-peak hours

## 📞 Support Resources

### MongoDB Atlas Support
- **Documentation**: [MongoDB Atlas Docs](https://docs.atlas.mongodb.com/)
- **Community Forums**: [MongoDB Community](https://community.mongodb.com/)
- **Support Tickets**: Available for paid tiers

### SisaRasa Migration Support
- **Migration Script**: Use `mongodb_atlas_migration.py`
- **Validation**: Run `database_migration_check.py`
- **Cleanup**: Use `pre_migration_cleanup.py`

## ✅ Pre-Migration Checklist

Before running the migration:
- [ ] Atlas cluster created and running
- [ ] Database user created with read/write permissions
- [ ] Network access configured (IP whitelisted)
- [ ] Connection string tested and working
- [ ] Local database validated
- [ ] Backup collections cleaned up (optional)
- [ ] Migration script ready (`mongodb_atlas_migration.py`)

## 🎯 Next Steps

Once Atlas is set up:
1. Run the migration script: `python mongodb_atlas_migration.py`
2. Validate the migration
3. Update your application configuration
4. Test all application features
5. Monitor performance in Atlas dashboard

---

**🔒 Security Note**: Never commit connection strings with passwords to version control. Use environment variables or secure configuration management.

**💡 Cost Note**: M0 (Free tier) includes:
- 512 MB storage
- Shared RAM and vCPU
- No backup/restore
- Community support

For production, consider upgrading to M2+ for dedicated resources and additional features.
