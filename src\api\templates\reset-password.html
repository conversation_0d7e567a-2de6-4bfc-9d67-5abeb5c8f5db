<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Reset Password | Sisa Rasa</title>

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
  <!-- Box Icons -->
  <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>
  <!-- SweetAlert for notifications -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
  <!-- GSAP for animations -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.5.1/gsap.min.js" defer></script>

  <style>
    :root {
      --first-color: #f1ead1;
      --first-color-dark: #e1cc7f;
      --first-color-darken: #f9e59a;
      --white-color: #0b0a0a;
    }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: var(--first-color);
      margin: var(--header-height) 0 0 0;
      padding: 0;
    }

    .l-header {
      width: 100%;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 100;
      background-color: var(--first-color);
      box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    .nav {
      height: var(--header-height);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 1rem;
    }

    .nav__list {
      display: flex;
      padding: 0;
      list-style: none;
      margin: 0;
    }

    .nav__item {
      margin-left: 2rem;
    }

    .nav__link {
      font-weight: 600;
      color: var(--white-color);
      text-decoration: none;
      transition: color 0.3s;
      font-family: 'Poppins', sans-serif;
    }

    .nav__link:hover {
      color: var(--first-color-dark);
    }

    .main {
      min-height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 80px 20px 40px;
    }

    .card {
      max-width: 900px;
      width: 100%;
      border: none;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .card img {
      max-height: 500px;
      width: 100%;
      object-fit: contain;
      padding: 1rem;
    }

    .btn-custom {
      background-color: var(--first-color-dark);
      color: var(--white-color);
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-custom:hover {
      background-color: var(--first-color-darken);
      transform: translateY(-2px);
    }

    .footer {
      padding: 2rem 0 1rem;
      text-align: center;
      background-color: var(--first-color);
    }

    .footer__copy {
      font-size: 0.875rem;
      color: #666;
    }

    /* Form validation styles */
    .form-control:focus {
      border-color: var(--first-color-dark);
      box-shadow: 0 0 0 0.25rem rgba(225, 204, 127, 0.25);
    }

    .invalid-feedback {
      font-size: 0.8rem;
    }

    /* Loading indicator */
    .spinner-border-sm {
      width: 1rem;
      height: 1rem;
      border-width: 0.15em;
      margin-right: 0.5rem;
      display: none;
    }

    .alert {
      border-radius: 0.5rem;
      margin-bottom: 1rem;
    }

    @media screen and (min-width: 768px) {
      :root {
        --header-height: 4rem;
      }

      .nav__list {
        display: flex;
      }

      .nav__item {
        margin-left: 3rem;
      }
    }
  </style>
</head>
<body>

  <!-- Header -->
  <header class="l-header">
    <nav class="nav container">
      <a href="/" class="nav__logo">
        <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Sisa Rasa Logo" width="120" height="auto">
      </a>

      <ul class="nav__list">
        <li class="nav__item"><a href="/" class="nav__link">Home</a></li>
        <li class="nav__item"><a href="/#about" class="nav__link">About</a></li>
        <li class="nav__item"><a href="/login" class="nav__link">Login</a></li>
        <li class="nav__item"><a href="/#contact" class="nav__link">Contact</a></li>
      </ul>
    </nav>
  </header>

  <!-- Reset Password Form -->
  <main class="main">
    <div class="card flex-lg-row flex-column">
      <!-- Image Section -->
      <div class="col-lg-6 p-0 d-none d-lg-block">
        <img src="{{ url_for('static', filename='images/logo2.png') }}" alt="Reset Password Visual" class="img-fluid w-100 h-100">
      </div>

      <!-- Form Section -->
      <div class="col-lg-6 p-4 p-lg-5 d-flex flex-column justify-content-center bg-white">
        <h2 class="text-center mb-2">Reset Password</h2>
        <p class="text-muted text-center mb-4" id="emailDisplay">Enter your new password below</p>

        <!-- Token validation alert -->
        <div id="tokenAlert" class="alert alert-warning d-none">
          <i class='bx bx-error-circle'></i>
          <span id="tokenMessage">Validating reset token...</span>
        </div>

        <form id="resetPasswordForm" class="needs-validation d-none" novalidate>
          <div class="mb-3">
            <label for="password" class="form-label">New Password</label>
            <div class="input-group">
              <input type="password" class="form-control" id="password" placeholder="••••••••" required minlength="6" />
              <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                <i class='bx bx-hide'></i>
              </button>
              <div class="invalid-feedback">Password must be at least 6 characters</div>
            </div>
          </div>

          <div class="mb-3">
            <label for="confirmPassword" class="form-label">Confirm New Password</label>
            <div class="input-group">
              <input type="password" class="form-control" id="confirmPassword" placeholder="••••••••" required minlength="6" />
              <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                <i class='bx bx-hide'></i>
              </button>
              <div class="invalid-feedback">Please confirm your password</div>
            </div>
          </div>

          <button type="submit" class="btn btn-custom w-100">
            <span class="spinner-border spinner-border-sm" id="resetSpinner" role="status" aria-hidden="true"></span>
            Reset Password
          </button>

          <div class="text-center mt-3">
            <a href="/login" class="text-decoration-none small">Back to Login</a>
          </div>
        </form>

        <!-- Invalid token message -->
        <div id="invalidTokenMessage" class="text-center d-none">
          <div class="alert alert-danger">
            <i class='bx bx-error-circle'></i>
            This reset link is invalid or has expired.
          </div>
          <a href="/forgot-password" class="btn btn-custom">Request New Reset Link</a>
          <div class="mt-3">
            <a href="/login" class="text-decoration-none small">Back to Login</a>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <p class="footer__copy">© 2025 Sisa Rasa. All rights reserved.</p>
    </div>
  </footer>

  <!-- JS -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get token from URL parameters
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');

      // Form elements
      const form = document.getElementById('resetPasswordForm');
      const passwordInput = document.getElementById('password');
      const confirmPasswordInput = document.getElementById('confirmPassword');
      const togglePasswordBtn = document.getElementById('togglePassword');
      const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPassword');
      const resetSpinner = document.getElementById('resetSpinner');
      const tokenAlert = document.getElementById('tokenAlert');
      const tokenMessage = document.getElementById('tokenMessage');
      const emailDisplay = document.getElementById('emailDisplay');
      const invalidTokenMessage = document.getElementById('invalidTokenMessage');

      // Check if token exists
      if (!token) {
        showInvalidToken();
        return;
      }

      // Verify token
      verifyToken(token);

      // Toggle password visibility
      togglePasswordBtn.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        this.querySelector('i').classList.toggle('bx-hide');
        this.querySelector('i').classList.toggle('bx-show');
      });

      toggleConfirmPasswordBtn.addEventListener('click', function() {
        const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmPasswordInput.setAttribute('type', type);
        this.querySelector('i').classList.toggle('bx-hide');
        this.querySelector('i').classList.toggle('bx-show');
      });

      // Form submission
      form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!form.checkValidity()) {
          e.stopPropagation();
          form.classList.add('was-validated');
          return;
        }

        // Check if passwords match
        if (passwordInput.value !== confirmPasswordInput.value) {
          Swal.fire({
            icon: 'error',
            title: 'Passwords Do Not Match',
            text: 'Please make sure your passwords match.',
            confirmButtonColor: '#e1cc7f'
          });
          return;
        }

        // Show loading spinner
        resetSpinner.style.display = 'inline-block';

        // Call the API
        fetch("/api/auth/reset-password", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ 
            token: token,
            password: passwordInput.value 
          })
        })
        .then(res => res.json())
        .then(data => {
          if (data.status === 'success') {
            Swal.fire({
              icon: 'success',
              title: 'Password Reset Successful!',
              text: data.message,
              confirmButtonColor: '#e1cc7f'
            }).then(() => {
              // Redirect to login
              window.location.href = '/login';
            });
          } else {
            throw new Error(data.message || 'Failed to reset password');
          }
        })
        .catch(err => {
          console.error(err);
          Swal.fire({
            icon: 'error',
            title: 'Reset Failed',
            text: err.message || 'Failed to reset password. Please try again.',
            confirmButtonColor: '#e1cc7f'
          });
        })
        .finally(() => {
          // Hide loading spinner
          resetSpinner.style.display = 'none';
        });
      });

      function verifyToken(token) {
        tokenAlert.classList.remove('d-none');
        
        fetch("/api/auth/verify-reset-token", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ token: token })
        })
        .then(res => res.json())
        .then(data => {
          if (data.status === 'success' && data.valid) {
            // Token is valid, show the form
            tokenAlert.classList.add('d-none');
            form.classList.remove('d-none');
            emailDisplay.textContent = `Reset password for ${data.email}`;
            
            // GSAP animations
            gsap.from('.card', {opacity: 0, y: 30, duration: 1, delay: 0.5});
            gsap.from('h2, p.text-muted', {opacity: 0, y: 20, duration: 0.8, delay: 0.8, stagger: 0.2});
            gsap.from('.form-control, .btn-custom', {opacity: 0, y: 10, duration: 0.6, delay: 1, stagger: 0.1});
          } else {
            // Token is invalid
            showInvalidToken();
          }
        })
        .catch(err => {
          console.error(err);
          showInvalidToken();
        });
      }

      function showInvalidToken() {
        tokenAlert.classList.add('d-none');
        form.classList.add('d-none');
        invalidTokenMessage.classList.remove('d-none');
      }
    });
  </script>
</body>
</html>
