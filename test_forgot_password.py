#!/usr/bin/env python3
"""
Test script for the forgot password functionality.

This script tests the complete forgot password workflow including:
1. Requesting a password reset
2. Verifying the reset token
3. Resetting the password
"""

import requests
import json
import sys

# API base URL
BASE_URL = "http://localhost:5000/api/auth"

def test_forgot_password_workflow():
    """Test the complete forgot password workflow."""
    
    print("🔐 Testing Forgot Password Workflow")
    print("=" * 50)
    
    # Test email (should exist in the database)
    test_email = "<EMAIL>"  # This user exists in the database
    new_password = "newpassword123"
    
    # Step 1: Request password reset
    print(f"\n1. Requesting password reset for: {test_email}")
    
    forgot_response = requests.post(
        f"{BASE_URL}/forgot-password",
        json={"email": test_email},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {forgot_response.status_code}")
    print(f"Response: {json.dumps(forgot_response.json(), indent=2)}")
    
    if forgot_response.status_code != 200:
        print("❌ Failed to request password reset")
        return False
    
    forgot_data = forgot_response.json()
    
    # Check if we got a reset token (development mode)
    if 'reset_token' not in forgot_data:
        print("❌ No reset token received (email might not exist or email is configured)")
        return False
    
    reset_token = forgot_data['reset_token']
    print(f"✅ Reset token received: {reset_token[:20]}...")
    
    # Step 2: Verify the reset token
    print(f"\n2. Verifying reset token")
    
    verify_response = requests.post(
        f"{BASE_URL}/verify-reset-token",
        json={"token": reset_token},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {verify_response.status_code}")
    print(f"Response: {json.dumps(verify_response.json(), indent=2)}")
    
    if verify_response.status_code != 200:
        print("❌ Failed to verify reset token")
        return False
    
    verify_data = verify_response.json()
    
    if not verify_data.get('valid'):
        print("❌ Reset token is not valid")
        return False
    
    print(f"✅ Reset token is valid for: {verify_data.get('email')}")
    
    # Step 3: Reset the password
    print(f"\n3. Resetting password")
    
    reset_response = requests.post(
        f"{BASE_URL}/reset-password",
        json={
            "token": reset_token,
            "password": new_password
        },
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {reset_response.status_code}")
    print(f"Response: {json.dumps(reset_response.json(), indent=2)}")
    
    if reset_response.status_code != 200:
        print("❌ Failed to reset password")
        return False
    
    reset_data = reset_response.json()
    
    if reset_data.get('status') != 'success':
        print("❌ Password reset was not successful")
        return False
    
    print("✅ Password reset successful!")
    
    # Step 4: Test login with new password
    print(f"\n4. Testing login with new password")
    
    login_response = requests.post(
        f"{BASE_URL}/login",
        json={
            "email": test_email,
            "password": new_password
        },
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {login_response.status_code}")
    print(f"Response: {json.dumps(login_response.json(), indent=2)}")
    
    if login_response.status_code != 200:
        print("❌ Failed to login with new password")
        return False
    
    login_data = login_response.json()
    
    if login_data.get('status') != 'success':
        print("❌ Login was not successful")
        return False
    
    print("✅ Login with new password successful!")
    
    # Step 5: Test that old token is no longer valid
    print(f"\n5. Testing that reset token is now invalid")
    
    verify_response2 = requests.post(
        f"{BASE_URL}/verify-reset-token",
        json={"token": reset_token},
        headers={"Content-Type": "application/json"}
    )
    
    verify_data2 = verify_response2.json()
    
    if verify_data2.get('valid'):
        print("❌ Reset token should be invalid after use")
        return False
    
    print("✅ Reset token is now invalid (as expected)")
    
    print("\n🎉 All tests passed! Forgot password workflow is working correctly.")
    return True

def test_invalid_scenarios():
    """Test invalid scenarios for forgot password."""
    
    print("\n🔍 Testing Invalid Scenarios")
    print("=" * 50)
    
    # Test 1: Invalid email
    print("\n1. Testing with non-existent email")
    
    response = requests.post(
        f"{BASE_URL}/forgot-password",
        json={"email": "<EMAIL>"},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    # Should still return success for security
    if response.status_code == 200 and response.json().get('status') == 'success':
        print("✅ Non-existent email handled correctly (security)")
    else:
        print("❌ Non-existent email not handled correctly")
    
    # Test 2: Invalid token
    print("\n2. Testing with invalid reset token")
    
    response = requests.post(
        f"{BASE_URL}/verify-reset-token",
        json={"token": "invalid-token-123"},
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 200 and not response.json().get('valid'):
        print("✅ Invalid token handled correctly")
    else:
        print("❌ Invalid token not handled correctly")
    
    # Test 3: Password too short
    print("\n3. Testing with password too short")
    
    response = requests.post(
        f"{BASE_URL}/reset-password",
        json={
            "token": "some-token",
            "password": "123"
        },
        headers={"Content-Type": "application/json"}
    )
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    
    if response.status_code == 400:
        print("✅ Short password rejected correctly")
    else:
        print("❌ Short password not rejected")

if __name__ == "__main__":
    try:
        # Test the main workflow
        success = test_forgot_password_workflow()
        
        # Test invalid scenarios
        test_invalid_scenarios()
        
        if success:
            print("\n✅ All tests completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API server. Make sure it's running on http://localhost:5000")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
