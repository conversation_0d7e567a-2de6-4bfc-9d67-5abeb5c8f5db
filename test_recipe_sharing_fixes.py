#!/usr/bin/env python3
"""
Test script to verify the recipe sharing fixes:
1. User profile header in shared-recipe.html shows dynamic user info
2. Recipe posts are excluded from general community feed
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def test_login():
    """Test login and get auth token"""
    print("Testing login...")
    
    # Try to login with a test user
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            print(f"✅ Login successful for user: {data['user']['name']}")
            return data['token']
        else:
            print(f"❌ Login failed: {data.get('message')}")
            return None
    else:
        print(f"❌ Login request failed with status {response.status_code}")
        return None

def test_user_profile_endpoint(token):
    """Test the /api/auth/me endpoint to verify user profile data"""
    print("\nTesting user profile endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            user = data['user']
            print(f"✅ User profile endpoint working:")
            print(f"   - Name: {user.get('name')}")
            print(f"   - Email: {user.get('email')}")
            print(f"   - Profile Image: {'Yes' if user.get('profile_image') else 'No'}")
            return True
        else:
            print(f"❌ Profile endpoint failed: {data.get('message')}")
            return False
    else:
        print(f"❌ Profile request failed with status {response.status_code}")
        return False

def test_community_posts_endpoint(token):
    """Test the /api/community/posts endpoint to verify recipe posts are excluded"""
    print("\nTesting community posts endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/community/posts", headers=headers)
    
    if response.status_code == 200:
        posts = response.json()
        print(f"✅ Community posts endpoint working:")
        print(f"   - Total posts: {len(posts)}")
        
        # Check if any posts have post_type 'shared_recipe'
        recipe_posts = [post for post in posts if post.get('post_type') == 'shared_recipe']
        
        if len(recipe_posts) == 0:
            print(f"✅ No recipe posts found in general community feed (as expected)")
        else:
            print(f"❌ Found {len(recipe_posts)} recipe posts in general community feed (should be 0)")
            for post in recipe_posts:
                print(f"   - Recipe post: {post.get('recipe_name', 'Unknown')}")
        
        return len(recipe_posts) == 0
    else:
        print(f"❌ Community posts request failed with status {response.status_code}")
        return False

def test_recipe_posts_endpoint(token):
    """Test the new /api/community/recipe-posts endpoint"""
    print("\nTesting recipe posts endpoint...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/community/recipe-posts", headers=headers)
    
    if response.status_code == 200:
        posts = response.json()
        print(f"✅ Recipe posts endpoint working:")
        print(f"   - Total recipe posts: {len(posts)}")
        
        # Check if all posts have post_type 'shared_recipe'
        non_recipe_posts = [post for post in posts if post.get('post_type') != 'shared_recipe']
        
        if len(non_recipe_posts) == 0:
            print(f"✅ All posts in recipe posts endpoint are recipe posts (as expected)")
        else:
            print(f"❌ Found {len(non_recipe_posts)} non-recipe posts in recipe posts endpoint")
        
        # Show some recipe post details
        for i, post in enumerate(posts[:3]):  # Show first 3
            print(f"   - Recipe {i+1}: {post.get('recipe_name', 'Unknown')}")
        
        return len(non_recipe_posts) == 0
    else:
        print(f"❌ Recipe posts request failed with status {response.status_code}")
        return False

def test_shared_recipe_page():
    """Test that the shared recipe page loads correctly"""
    print("\nTesting shared recipe page...")
    
    response = requests.get(f"{BASE_URL}/shared-recipe")
    
    if response.status_code == 200:
        content = response.text
        
        # Check if the page contains the dynamic user profile loading code
        if 'loadUserProfile()' in content:
            print("✅ Shared recipe page contains dynamic user profile loading code")
            return True
        else:
            print("❌ Shared recipe page missing dynamic user profile loading code")
            return False
    else:
        print(f"❌ Shared recipe page request failed with status {response.status_code}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Recipe Sharing Fixes")
    print("=" * 50)
    
    # Test 1: Login and get token
    token = test_login()
    if not token:
        print("\n❌ Cannot proceed without authentication token")
        sys.exit(1)
    
    # Test 2: User profile endpoint
    profile_test = test_user_profile_endpoint(token)
    
    # Test 3: Community posts endpoint (should exclude recipe posts)
    community_test = test_community_posts_endpoint(token)
    
    # Test 4: Recipe posts endpoint (should only include recipe posts)
    recipe_posts_test = test_recipe_posts_endpoint(token)
    
    # Test 5: Shared recipe page (should have dynamic user profile loading)
    page_test = test_shared_recipe_page()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"   - User Profile Endpoint: {'✅ PASS' if profile_test else '❌ FAIL'}")
    print(f"   - Community Posts (no recipes): {'✅ PASS' if community_test else '❌ FAIL'}")
    print(f"   - Recipe Posts Endpoint: {'✅ PASS' if recipe_posts_test else '❌ FAIL'}")
    print(f"   - Shared Recipe Page: {'✅ PASS' if page_test else '❌ FAIL'}")
    
    all_passed = all([profile_test, community_test, recipe_posts_test, page_test])
    
    if all_passed:
        print("\n🎉 All tests passed! Recipe sharing fixes are working correctly.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    main()
