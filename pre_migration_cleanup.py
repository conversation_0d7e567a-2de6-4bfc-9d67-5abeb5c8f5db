#!/usr/bin/env python3
"""
Pre-Migration Database Cleanup Script for SisaRasa MongoDB Atlas Migration

This script prepares the database for migration by:
1. Removing backup collections
2. Cleaning up empty collections
3. Optimizing indexes
4. Creating a final backup before cleanup
5. Validating data integrity
"""

import os
import sys
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
from dotenv import load_dotenv
import json

# Load environment variables
load_dotenv()

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/sisarasa')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        client.admin.command('ping')
        print(f"✅ Connected to database: {db.name}")
        return db, client
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        sys.exit(1)

def create_pre_cleanup_backup(db):
    """Create a backup before cleanup operations."""
    print("\n📦 CREATING PRE-CLEANUP BACKUP")
    print("=" * 60)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"pre_migration_backup_{timestamp}"
    
    # Get critical collections
    critical_collections = ['users', 'recipes', 'recipe_reviews', 'community_posts', 'review_votes']
    
    backup_info = {
        'timestamp': timestamp,
        'collections': {},
        'total_documents': 0
    }
    
    for collection_name in critical_collections:
        if collection_name in db.list_collection_names():
            count = db[collection_name].count_documents({})
            backup_info['collections'][collection_name] = count
            backup_info['total_documents'] += count
            print(f"   📁 {collection_name}: {count:,} documents")
    
    # Save backup info
    with open(f'{backup_name}_info.json', 'w') as f:
        json.dump(backup_info, f, indent=2, default=str)
    
    print(f"\n✅ Backup info saved: {backup_name}_info.json")
    print(f"📊 Total documents backed up: {backup_info['total_documents']:,}")
    
    return backup_name, backup_info

def identify_collections_for_cleanup(db):
    """Identify collections that can be safely removed."""
    print("\n🔍 IDENTIFYING CLEANUP CANDIDATES")
    print("=" * 60)
    
    collections = db.list_collection_names()
    
    # Collections to keep (core functionality)
    core_collections = {
        'users', 'recipes', 'recipe_reviews', 'community_posts', 
        'post_comments', 'post_likes', 'comment_likes', 'review_votes',
        'recipe_verifications', 'recipe_likes', 'shared_recipes'
    }
    
    backup_collections = []
    empty_collections = []
    small_unused_collections = []
    
    for collection_name in collections:
        try:
            count = db[collection_name].count_documents({})
            
            # Identify backup collections
            if any(keyword in collection_name.lower() for keyword in ['backup', '_bak', '_old']):
                backup_collections.append((collection_name, count))
            
            # Identify empty collections
            elif count == 0:
                empty_collections.append(collection_name)
            
            # Identify small collections that might be unused
            elif collection_name not in core_collections and count < 10:
                small_unused_collections.append((collection_name, count))
                
        except Exception as e:
            print(f"   ❌ Error analyzing {collection_name}: {e}")
    
    print(f"\n📦 Backup Collections ({len(backup_collections)}):")
    for name, count in backup_collections:
        print(f"   - {name}: {count:,} documents")
    
    print(f"\n🗑️ Empty Collections ({len(empty_collections)}):")
    for name in empty_collections:
        print(f"   - {name}")
    
    print(f"\n📄 Small Unused Collections ({len(small_unused_collections)}):")
    for name, count in small_unused_collections:
        print(f"   - {name}: {count} documents")
    
    return {
        'backup_collections': backup_collections,
        'empty_collections': empty_collections,
        'small_unused_collections': small_unused_collections
    }

def cleanup_collections(db, cleanup_candidates):
    """Remove identified collections safely."""
    print("\n🧹 PERFORMING CLEANUP")
    print("=" * 60)
    
    total_removed = 0
    
    # Remove backup collections
    print("\n📦 Removing backup collections...")
    for collection_name, count in cleanup_candidates['backup_collections']:
        try:
            db[collection_name].drop()
            print(f"   ✅ Removed {collection_name} ({count:,} documents)")
            total_removed += count
        except Exception as e:
            print(f"   ❌ Failed to remove {collection_name}: {e}")
    
    # Remove empty collections
    print("\n🗑️ Removing empty collections...")
    for collection_name in cleanup_candidates['empty_collections']:
        try:
            db[collection_name].drop()
            print(f"   ✅ Removed {collection_name}")
        except Exception as e:
            print(f"   ❌ Failed to remove {collection_name}: {e}")
    
    # Ask user about small unused collections
    if cleanup_candidates['small_unused_collections']:
        print("\n📄 Small collections found. Review before removal:")
        for collection_name, count in cleanup_candidates['small_unused_collections']:
            print(f"   - {collection_name}: {count} documents")
        
        response = input("\nRemove these small collections? (y/N): ").lower().strip()
        if response == 'y':
            for collection_name, count in cleanup_candidates['small_unused_collections']:
                try:
                    db[collection_name].drop()
                    print(f"   ✅ Removed {collection_name} ({count} documents)")
                    total_removed += count
                except Exception as e:
                    print(f"   ❌ Failed to remove {collection_name}: {e}")
        else:
            print("   ⏭️ Skipped small collections removal")
    
    print(f"\n📊 Cleanup Summary:")
    print(f"   Total documents removed: {total_removed:,}")
    
    return total_removed

def optimize_indexes(db):
    """Optimize indexes for remaining collections."""
    print("\n🔧 OPTIMIZING INDEXES")
    print("=" * 60)
    
    # Core collections that should have optimized indexes
    core_collections = ['users', 'recipes', 'recipe_reviews', 'community_posts', 'review_votes']
    
    for collection_name in core_collections:
        if collection_name in db.list_collection_names():
            try:
                # Get current indexes
                indexes = list(db[collection_name].list_indexes())
                print(f"\n📁 {collection_name}:")
                print(f"   Current indexes: {len(indexes)}")
                
                # List index names
                for idx in indexes:
                    index_name = idx.get('name', 'unnamed')
                    print(f"   - {index_name}")
                    
            except Exception as e:
                print(f"   ❌ Error analyzing indexes for {collection_name}: {e}")

def validate_data_integrity(db):
    """Validate data integrity after cleanup."""
    print("\n✅ VALIDATING DATA INTEGRITY")
    print("=" * 60)
    
    # Check core collections
    core_collections = {
        'users': 'User accounts',
        'recipes': 'Recipe data', 
        'recipe_reviews': 'User reviews',
        'community_posts': 'Community content',
        'review_votes': 'Review interactions'
    }
    
    all_good = True
    
    for collection_name, description in core_collections.items():
        try:
            count = db[collection_name].count_documents({})
            print(f"   ✅ {description}: {count:,} documents")
            
            if count == 0 and collection_name in ['users', 'recipes']:
                print(f"   ⚠️ WARNING: {collection_name} is empty!")
                all_good = False
                
        except Exception as e:
            print(f"   ❌ Error checking {collection_name}: {e}")
            all_good = False
    
    return all_good

def main():
    """Main cleanup function."""
    print("🧹 PRE-MIGRATION DATABASE CLEANUP")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Connect to database
    db, client = connect_to_database()
    
    try:
        # Create backup
        backup_name, backup_info = create_pre_cleanup_backup(db)
        
        # Identify cleanup candidates
        cleanup_candidates = identify_collections_for_cleanup(db)
        
        # Show cleanup plan
        total_backup_docs = sum(count for _, count in cleanup_candidates['backup_collections'])
        total_empty = len(cleanup_candidates['empty_collections'])
        total_small = len(cleanup_candidates['small_unused_collections'])
        
        print(f"\n📋 CLEANUP PLAN SUMMARY")
        print("=" * 60)
        print(f"Backup collections to remove: {len(cleanup_candidates['backup_collections'])} ({total_backup_docs:,} docs)")
        print(f"Empty collections to remove: {total_empty}")
        print(f"Small collections to review: {total_small}")
        
        # Confirm cleanup
        if total_backup_docs > 0 or total_empty > 0:
            response = input(f"\nProceed with cleanup? (y/N): ").lower().strip()
            if response != 'y':
                print("❌ Cleanup cancelled by user")
                return
            
            # Perform cleanup
            total_removed = cleanup_collections(db, cleanup_candidates)
            
            # Optimize indexes
            optimize_indexes(db)
            
            # Validate integrity
            if validate_data_integrity(db):
                print(f"\n✅ Database cleanup completed successfully!")
                print(f"📊 Removed {total_removed:,} documents from backup collections")
                print(f"💾 Database is now ready for Atlas migration")
            else:
                print(f"\n⚠️ Data integrity issues detected. Please review before migration.")
        else:
            print(f"\n✅ No cleanup needed. Database is ready for migration.")
            
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
