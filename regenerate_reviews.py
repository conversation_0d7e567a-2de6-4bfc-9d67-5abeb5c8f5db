#!/usr/bin/env python3
"""
Regenerate Reviews Script for SisaRasa

This script regenerates reviews using only the recipes that actually exist in the database.
"""

import pymongo
import random
import numpy as np
from datetime import datetime, timedelta

# Review templates (simplified version)
REVIEW_TEMPLATES = {
    5: [
        "Absolutely amazing! This recipe is now a family favorite.",
        "Perfect recipe! Easy to follow and delicious results.",
        "Outstanding! Will definitely make this again.",
        "Incredible flavors! My guests loved it.",
        "Best recipe I've tried in a long time!",
        "Fantastic! Even my picky kids enjoyed it.",
        "Wonderful recipe with clear instructions.",
        "Exceeded my expectations! Highly recommend."
    ],
    4: [
        "Really good recipe! Made a few small adjustments.",
        "Great flavors, though it took longer than expected.",
        "Very tasty! Would make again with minor tweaks.",
        "Good recipe overall, easy to follow.",
        "Nice dish! Added some extra spices to taste.",
        "Solid recipe, turned out well.",
        "Pretty good! Family enjoyed it."
    ],
    3: [
        "Decent recipe, nothing special but okay.",
        "Average results. It was fine but not amazing.",
        "Okay recipe, might try variations next time.",
        "Not bad, but I've had better versions.",
        "Acceptable results, fairly easy to make.",
        "It was alright, nothing to write home about."
    ],
    2: [
        "Disappointing. Didn't turn out as expected.",
        "Not great. Instructions were unclear.",
        "Below average. Flavors were bland.",
        "Didn't work well for me. Too complicated.",
        "Poor results despite following instructions."
    ],
    1: [
        "Terrible recipe! Complete disaster.",
        "Awful! Couldn't even finish eating it.",
        "Worst recipe I've ever tried.",
        "Completely inedible. Don't waste your time.",
        "Horrible! Instructions made no sense."
    ]
}

def regenerate_reviews():
    """Regenerate reviews for existing recipes only."""
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🔄 Regenerating reviews for existing recipes...")
    
    # Clear existing reviews
    print("🗑️  Clearing existing reviews...")
    deleted_count = db.recipe_reviews.delete_many({}).deleted_count
    print(f"   Deleted {deleted_count} old reviews")
    
    # Get all recipes from database
    recipes = list(db.recipes.find({}, {'original_id': 1, 'name': 1}))
    print(f"📖 Found {len(recipes)} recipes in database")
    
    # Get all users
    users = list(db.users.find({}, {'_id': 1, 'name': 1, 'created_at': 1}))
    print(f"👥 Found {len(users)} users for review generation")
    
    if not users:
        print("❌ No users found. Cannot generate reviews.")
        client.close()
        return
    
    reviews_to_insert = []
    
    # Generate reviews for 70% of recipes
    recipes_to_review = random.sample(recipes, int(len(recipes) * 0.7))
    
    for recipe in recipes_to_review:
        recipe_id = str(recipe.get('original_id', ''))
        if not recipe_id or recipe_id == 'None':
            continue

        # Debug: Print what we're using
        print(f"   Creating reviews for recipe ID: {recipe_id} - {recipe.get('name', 'Unknown')[:50]}...")
            
        # Generate 1-8 reviews per recipe
        num_reviews = random.randint(1, 8)
        
        # Select random users for this recipe
        recipe_reviewers = random.sample(users, min(len(users), num_reviews))
        
        for user in recipe_reviewers:
            user_id = str(user['_id'])
            user_name = user.get('name', 'Anonymous User')
            
            # Generate rating (weighted towards positive)
            rating = int(np.random.choice([1, 2, 3, 4, 5], p=[0.05, 0.1, 0.15, 0.35, 0.35]))
            
            # Generate review text
            review_text = random.choice(REVIEW_TEMPLATES[rating])
            
            # Generate review date (within last year, after user creation)
            user_created = user.get('created_at', datetime.utcnow())
            days_since_creation = (datetime.utcnow() - user_created).days
            if days_since_creation > 0:
                review_days_ago = random.randint(1, min(days_since_creation, 365))
                review_date = datetime.utcnow() - timedelta(days=review_days_ago)
            else:
                review_date = datetime.utcnow()
            
            review = {
                'recipe_id': recipe_id,
                'user_id': user_id,
                'user_name': user_name,
                'rating': rating,
                'review_text': review_text,
                'helpful_votes': random.randint(0, 5),
                'unhelpful_votes': random.randint(0, 2),
                'created_at': review_date,
                'updated_at': review_date
            }
            
            reviews_to_insert.append(review)
    
    # Insert reviews in batches
    if reviews_to_insert:
        print(f"💾 Inserting {len(reviews_to_insert)} new reviews...")
        result = db.recipe_reviews.insert_many(reviews_to_insert)
        print(f"✅ Successfully inserted {len(result.inserted_ids)} reviews")
    else:
        print("❌ No reviews to insert")
    
    # Show statistics
    total_reviews = db.recipe_reviews.count_documents({})
    recipes_with_reviews = len(db.recipe_reviews.distinct('recipe_id'))
    
    print(f"\n📊 Final Statistics:")
    print(f"   Total reviews: {total_reviews}")
    print(f"   Recipes with reviews: {recipes_with_reviews}")
    print(f"   Average reviews per recipe: {total_reviews / recipes_with_reviews:.1f}")
    
    # Test a sample
    print(f"\n🧪 Testing sample recipe with reviews:")
    sample_review = db.recipe_reviews.find_one({})
    if sample_review:
        recipe_id = sample_review['recipe_id']
        recipe = db.recipes.find_one({'original_id': recipe_id})
        if recipe:
            recipe_name = recipe.get('name', 'Unknown')
            review_count = db.recipe_reviews.count_documents({'recipe_id': recipe_id})
            print(f"   Recipe: {recipe_name}")
            print(f"   Recipe ID: {recipe_id}")
            print(f"   Reviews: {review_count}")
            print(f"   ✅ Recipe and reviews are properly linked!")
        else:
            print(f"   ❌ Recipe {recipe_id} not found in database")
    
    client.close()
    print(f"\n🎉 Review regeneration completed!")

if __name__ == "__main__":
    print("SisaRasa Review Regeneration")
    print("=" * 50)
    
    response = input("This will delete all existing reviews and create new ones. Continue? (y/N): ")
    if response.lower() == 'y':
        regenerate_reviews()
    else:
        print("Operation cancelled.")
