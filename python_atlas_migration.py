#!/usr/bin/env python3
"""
Pure Python Migration Script for SisaRasa to MongoDB Atlas
No external tools required - uses PyMongo only
"""

import os
import sys
import time
import json
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId

# Your Atlas connection string (with database name)
ATLAS_URI = "mongodb+srv://farahfiqh:<EMAIL>/sisarasa?retryWrites=true&w=majority&appName=SisaRasa"
LOCAL_URI = "mongodb://localhost:27017/sisarasa"

def log(message, level="INFO"):
    """Log migration steps."""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {level}: {message}")

def test_connections():
    """Test both local and Atlas connections."""
    log("🔍 TESTING CONNECTIONS")
    log("=" * 60)
    
    # Test local connection
    try:
        local_client = MongoClient(LOCAL_URI, serverSelectionTimeoutMS=5000)
        local_db = local_client.get_default_database()
        local_client.admin.command('ping')
        
        collections = local_db.list_collection_names()
        total_docs = sum(local_db[col].count_documents({}) for col in collections)
        
        log(f"✅ Local database: {local_db.name}")
        log(f"📊 Collections: {len(collections)}, Documents: {total_docs:,}")
        
    except Exception as e:
        log(f"❌ Local connection failed: {e}", "ERROR")
        return False, None, None
    
    # Test Atlas connection
    try:
        atlas_client = MongoClient(ATLAS_URI, serverSelectionTimeoutMS=10000)
        atlas_db = atlas_client.get_default_database()
        
        start_time = time.time()
        atlas_client.admin.command('ping')
        connection_time = (time.time() - start_time) * 1000
        
        log(f"✅ Atlas database: {atlas_db.name}")
        log(f"📊 Connection time: {connection_time:.2f}ms")
        
        return True, (local_db, local_client), (atlas_db, atlas_client)
        
    except Exception as e:
        log(f"❌ Atlas connection failed: {e}", "ERROR")
        local_client.close()
        return False, None, None

def migrate_collection(local_db, atlas_db, collection_name):
    """Migrate a single collection from local to Atlas."""
    try:
        # Get document count
        doc_count = local_db[collection_name].count_documents({})
        if doc_count == 0:
            log(f"⚠️ {collection_name}: Empty collection, skipping")
            return True
        
        log(f"🔄 Migrating {collection_name}: {doc_count:,} documents")
        
        # Drop existing collection in Atlas
        atlas_db[collection_name].drop()
        
        # Migrate in batches
        batch_size = 1000
        migrated = 0
        
        cursor = local_db[collection_name].find()
        batch = []
        
        for doc in cursor:
            batch.append(doc)
            
            if len(batch) >= batch_size:
                atlas_db[collection_name].insert_many(batch)
                migrated += len(batch)
                log(f"   📤 {collection_name}: {migrated:,}/{doc_count:,} documents")
                batch = []
        
        # Insert remaining documents
        if batch:
            atlas_db[collection_name].insert_many(batch)
            migrated += len(batch)
        
        log(f"✅ {collection_name}: {migrated:,} documents migrated")
        return True
        
    except Exception as e:
        log(f"❌ {collection_name} migration failed: {e}", "ERROR")
        return False

def migrate_indexes(local_db, atlas_db, collection_name):
    """Migrate indexes for a collection."""
    try:
        # Get indexes from local collection
        local_indexes = list(local_db[collection_name].list_indexes())
        
        for index in local_indexes:
            # Skip the default _id index
            if index['name'] == '_id_':
                continue
                
            # Create index in Atlas
            index_keys = index['key']
            index_options = {k: v for k, v in index.items() 
                           if k not in ['key', 'v', 'ns']}
            
            try:
                atlas_db[collection_name].create_index(
                    list(index_keys.items()), 
                    **index_options
                )
                log(f"   📋 Created index: {index['name']}")
            except Exception as e:
                log(f"   ⚠️ Index creation failed for {index['name']}: {e}", "WARNING")
        
        return True
        
    except Exception as e:
        log(f"❌ Index migration failed for {collection_name}: {e}", "ERROR")
        return False

def validate_migration(local_db, atlas_db):
    """Validate migration by comparing data counts."""
    log("\n✅ VALIDATING MIGRATION")
    log("=" * 60)
    
    local_collections = [c for c in local_db.list_collection_names() 
                        if 'backup' not in c.lower()]
    
    validation_passed = True
    total_local = 0
    total_atlas = 0
    
    for collection_name in local_collections:
        try:
            local_count = local_db[collection_name].count_documents({})
            atlas_count = atlas_db[collection_name].count_documents({})
            
            total_local += local_count
            total_atlas += atlas_count
            
            if local_count == atlas_count:
                log(f"✅ {collection_name}: {atlas_count:,} documents (matches)")
            else:
                log(f"❌ {collection_name}: Local={local_count:,}, Atlas={atlas_count:,}", "ERROR")
                validation_passed = False
                
        except Exception as e:
            log(f"❌ Error validating {collection_name}: {e}", "ERROR")
            validation_passed = False
    
    log(f"\n📊 TOTAL: Local={total_local:,}, Atlas={total_atlas:,}")
    
    if validation_passed:
        log("🎉 All collections validated successfully!")
    else:
        log("⚠️ Some validation issues found", "WARNING")
    
    return validation_passed

def update_env_file():
    """Update .env file with Atlas connection string."""
    log("\n⚙️ UPDATING .ENV FILE")
    log("=" * 60)
    
    try:
        # Backup current .env
        if os.path.exists('.env'):
            import shutil
            shutil.copy('.env', '.env.backup')
            log("📦 Created .env backup")
        
        # Read current .env
        env_lines = []
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_lines = f.readlines()
        
        # Update MONGO_URI
        updated = False
        for i, line in enumerate(env_lines):
            if line.startswith('MONGO_URI='):
                env_lines[i] = f"MONGO_URI={ATLAS_URI}\n"
                updated = True
                break
        
        if not updated:
            env_lines.append(f"MONGO_URI={ATLAS_URI}\n")
        
        # Write updated .env
        with open('.env', 'w') as f:
            f.writelines(env_lines)
        
        log("✅ Updated .env file with Atlas connection string")
        return True
        
    except Exception as e:
        log(f"❌ Failed to update .env file: {e}", "ERROR")
        return False

def main():
    """Execute the migration."""
    log("🚀 SISARASA MONGODB ATLAS MIGRATION (Python-Only)")
    log("=" * 60)
    log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test connections
    success, local_conn, atlas_conn = test_connections()
    if not success:
        return False
    
    local_db, local_client = local_conn
    atlas_db, atlas_client = atlas_conn
    
    try:
        # Get collections to migrate (exclude backups)
        collections_to_migrate = [c for c in local_db.list_collection_names() 
                                if 'backup' not in c.lower()]
        
        log(f"\n📦 MIGRATING {len(collections_to_migrate)} COLLECTIONS")
        log("=" * 60)
        
        # Migrate each collection
        migration_success = True
        for collection_name in collections_to_migrate:
            if not migrate_collection(local_db, atlas_db, collection_name):
                migration_success = False
            
            # Migrate indexes
            migrate_indexes(local_db, atlas_db, collection_name)
        
        if not migration_success:
            log("❌ Some collections failed to migrate", "ERROR")
            return False
        
        # Validate migration
        if not validate_migration(local_db, atlas_db):
            log("❌ Migration validation failed", "ERROR")
            return False
        
        # Update .env file
        if not update_env_file():
            log("⚠️ Failed to update .env file. Please update manually.", "WARNING")
        
        log("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
        log("=" * 60)
        log("✅ Your SisaRasa database is now running on MongoDB Atlas!")
        log("✅ Your application will now use the Atlas connection.")
        log("✅ Test your application to ensure everything works correctly.")
        
        return True
        
    except Exception as e:
        log(f"❌ Migration failed: {e}", "ERROR")
        return False
        
    finally:
        # Close connections
        local_client.close()
        atlas_client.close()

if __name__ == "__main__":
    print("🌟 SisaRasa MongoDB Atlas Migration (Python-Only)")
    print("=" * 60)
    print("This will migrate your local MongoDB to Atlas using pure Python.")
    print(f"Atlas Cluster: sisarasa.pzkt0dj.mongodb.net")
    print(f"Database User: farahfiqh")
    print("No external tools required - uses PyMongo only!")
    
    proceed = input("\nProceed with migration? (y/N): ").lower().strip()
    if proceed != 'y':
        print("❌ Migration cancelled by user")
        sys.exit(0)
    
    success = main()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("Your SisaRasa application is now connected to MongoDB Atlas.")
        print("\nNext steps:")
        print("1. Test your application")
        print("2. Run: python verify_migration.py")
    else:
        print("\n❌ Migration failed. Check the logs above for details.")
        print("Your local database remains unchanged.")
