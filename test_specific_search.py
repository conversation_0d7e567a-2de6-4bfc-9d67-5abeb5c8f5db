#!/usr/bin/env python3
"""
Test script to search for specific ingredients that exist in user recipes.
"""

import requests
import json

def test_specific_search():
    """Test search with ingredients from user recipes."""
    
    # Test with ingredients from user recipes we found
    test_cases = [
        {"ingredients": ["noodle", "cucumber"], "description": "mee goreng ingredients"},
        {"ingredients": ["cabbage", "carrot"], "description": "leftover vege stir ingredients"},
        {"ingredients": ["garlic", "soy sauce"], "description": "common Asian ingredients"},
        {"ingredients": ["test ingredient"], "description": "test recipe ingredient"},
    ]
    
    url = "http://localhost:5000/api/recommend"
    
    for test_case in test_cases:
        print(f"\n🔍 Testing search with {test_case['description']}: {test_case['ingredients']}")
        print("-" * 60)
        
        payload = {
            "ingredients": test_case['ingredients'],
            "limit": 15
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'ok':
                print(f"❌ API error: {data.get('message', 'Unknown error')}")
                continue
            
            recipes = data.get('recipes', [])
            user_recipes = [r for r in recipes if r.get('is_user_recipe', False)]
            system_recipes = [r for r in recipes if not r.get('is_user_recipe', False)]
            
            print(f"   Total results: {len(recipes)}")
            print(f"   System recipes: {len(system_recipes)}")
            print(f"   User recipes: {len(user_recipes)}")
            
            if user_recipes:
                print(f"   ✅ Found user recipes:")
                for recipe in user_recipes:
                    print(f"      - {recipe.get('name', 'Unknown')} (score: {recipe.get('score', 0):.3f})")
            else:
                print(f"   ⚠️  No user recipes found")
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

if __name__ == "__main__":
    print("Testing Search with User Recipe Ingredients...")
    print("=" * 60)
    test_specific_search()
    print("\n" + "=" * 60)
    print("Test completed!")
