#!/usr/bin/env python3
"""
Test script to verify delete button logic for community recipes.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def create_test_user_and_login():
    """Create a test user and login to get a token."""
    print("Creating test user and logging in...")
    
    import time
    timestamp = str(int(time.time()))
    test_user = {
        "name": "Delete Test User",
        "email": f"deletetest{timestamp}@example.com",
        "password": "testpassword123"
    }
    
    try:
        # Register the user
        register_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
        print(f"Register response: {register_response.status_code}")
        
        # Login
        login_data = {
            "email": test_user["email"],
            "password": test_user["password"]
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"Login response: {login_response.status_code}")
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result.get('token')
            user_info = result.get('user', {})
            print(f"✓ Successfully logged in")
            print(f"User ID: {user_info.get('id')}")
            print(f"User Name: {user_info.get('name')}")
            return token, user_info.get('id')
        else:
            print(f"✗ Login failed: {login_response.text}")
            return None, None
            
    except Exception as e:
        print(f"✗ Error during authentication: {e}")
        return None, None

def test_delete_button_logic(token, current_user_id):
    """Test the delete button logic by checking recipe ownership."""
    print(f"\n=== Testing Delete Button Logic ===")
    print(f"Current User ID: {current_user_id}")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Number of recipes returned: {len(data)}")
            
            if data:
                print("\n=== Recipe Ownership Analysis ===")
                owned_recipes = 0
                other_recipes = 0
                
                for i, recipe in enumerate(data):
                    recipe_user_id = recipe.get('user_id', '')
                    recipe_username = recipe.get('username', 'Unknown')
                    recipe_name = recipe.get('name', 'Untitled')
                    
                    is_owned = recipe_user_id == current_user_id
                    if is_owned:
                        owned_recipes += 1
                        print(f"✓ Recipe {i+1}: '{recipe_name}' by {recipe_username} - OWNED (delete button should show)")
                    else:
                        other_recipes += 1
                        print(f"  Recipe {i+1}: '{recipe_name}' by {recipe_username} - NOT OWNED (no delete button)")
                
                print(f"\n=== Summary ===")
                print(f"Recipes owned by current user: {owned_recipes}")
                print(f"Recipes owned by others: {other_recipes}")
                print(f"Total recipes: {len(data)}")
                
                if owned_recipes > 0:
                    print("✓ Delete buttons should appear for owned recipes")
                else:
                    print("ℹ No recipes owned by current user - delete buttons won't appear")
                
                return True
            else:
                print("No recipes found in response")
                return True
        else:
            print(f"✗ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error calling API: {e}")
        return False

def test_current_user_endpoint(token):
    """Test the current user endpoint to verify user ID consistency."""
    print("\n=== Testing Current User Endpoint ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            user_info = data.get('user', {})
            print(f"Current user ID from /api/auth/me: {user_info.get('id')}")
            print(f"Current user name: {user_info.get('name')}")
            return user_info.get('id')
        else:
            print(f"✗ API call failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error calling API: {e}")
        return None

def main():
    """Run delete button tests."""
    print("=== Delete Button Logic Test ===\n")
    
    # Get authentication token
    token, login_user_id = create_test_user_and_login()
    if not token:
        print("✗ Could not authenticate, stopping test")
        return 1
    
    # Verify current user endpoint
    me_user_id = test_current_user_endpoint(token)
    
    # Check if user IDs match
    if login_user_id != me_user_id:
        print(f"⚠ Warning: User ID mismatch between login ({login_user_id}) and /me endpoint ({me_user_id})")
        current_user_id = me_user_id  # Use the /me endpoint result
    else:
        print(f"✓ User ID consistent across endpoints: {login_user_id}")
        current_user_id = login_user_id
    
    # Test delete button logic
    delete_test_success = test_delete_button_logic(token, current_user_id)
    
    print(f"\n=== Test Results ===")
    print(f"Authentication: {'✓' if token else '✗'}")
    print(f"User ID consistency: {'✓' if login_user_id == me_user_id else '⚠'}")
    print(f"Delete button logic: {'✓' if delete_test_success else '✗'}")
    
    return 0 if delete_test_success else 1

if __name__ == "__main__":
    sys.exit(main())
