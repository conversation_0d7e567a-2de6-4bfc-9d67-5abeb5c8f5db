#!/usr/bin/env python3
"""
Fix Review Data Script for SisaRasa

This script fixes the existing review data by:
1. Removing reviews for non-existent recipes
2. Ensuring all reviews reference valid recipes in the database
3. Updating review counts to match actual data
"""

import pymongo
from datetime import datetime

def fix_review_data():
    """Fix review data to match existing recipes."""
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("🔧 Starting review data cleanup...")
    
    # Get all valid recipe IDs from the database
    valid_recipe_ids = set(str(id) for id in db.recipes.distinct('original_id'))
    print(f"📖 Found {len(valid_recipe_ids)} valid recipe IDs in database")
    
    # Get all recipe IDs that have reviews
    review_recipe_ids = set(db.recipe_reviews.distinct('recipe_id'))
    print(f"⭐ Found {len(review_recipe_ids)} unique recipe IDs in reviews")
    
    # Find invalid reviews (reviews for non-existent recipes)
    invalid_recipe_ids = review_recipe_ids - valid_recipe_ids
    print(f"❌ Found {len(invalid_recipe_ids)} invalid recipe IDs in reviews")
    
    if invalid_recipe_ids:
        # Count reviews to be deleted
        invalid_review_count = db.recipe_reviews.count_documents({
            'recipe_id': {'$in': list(invalid_recipe_ids)}
        })
        print(f"🗑️  Will delete {invalid_review_count} reviews for non-existent recipes")
        
        # Delete invalid reviews
        result = db.recipe_reviews.delete_many({
            'recipe_id': {'$in': list(invalid_recipe_ids)}
        })
        print(f"✅ Deleted {result.deleted_count} invalid reviews")
    
    # Get updated statistics
    remaining_reviews = db.recipe_reviews.count_documents({})
    valid_recipe_ids_with_reviews = len(set(db.recipe_reviews.distinct('recipe_id')))
    
    print("\n📊 Updated Statistics:")
    print(f"   Total reviews: {remaining_reviews}")
    print(f"   Recipes with reviews: {valid_recipe_ids_with_reviews}")
    print(f"   Average reviews per recipe: {remaining_reviews / valid_recipe_ids_with_reviews:.1f}")
    
    # Show some examples of recipes with reviews
    print("\n🔍 Sample recipes with reviews:")
    pipeline = [
        {'$group': {
            '_id': '$recipe_id',
            'review_count': {'$sum': 1},
            'avg_rating': {'$avg': '$rating'}
        }},
        {'$sort': {'review_count': -1}},
        {'$limit': 5}
    ]
    
    try:
        top_reviewed = list(db.recipe_reviews.aggregate(pipeline))
        for recipe_data in top_reviewed:
            recipe_id = recipe_data['_id']
            review_count = recipe_data['review_count']
            avg_rating = recipe_data['avg_rating']
            
            # Get recipe name
            recipe = db.recipes.find_one({'original_id': recipe_id})
            recipe_name = recipe.get('name', 'Unknown') if recipe else 'Recipe not found'
            
            print(f"   {recipe_name[:50]}... - {review_count} reviews, {avg_rating:.1f}/5")
    except Exception as e:
        print(f"   Error getting top reviewed recipes: {e}")
    
    client.close()
    print("\n✅ Review data cleanup completed!")

def test_review_api():
    """Test if reviews are now accessible via API."""
    client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = client.sisarasa
    
    print("\n🧪 Testing review API compatibility...")
    
    # Get a recipe that has reviews
    sample_review = db.recipe_reviews.find_one({})
    if sample_review:
        recipe_id = sample_review['recipe_id']
        
        # Check if recipe exists
        recipe = db.recipes.find_one({'original_id': recipe_id})
        if recipe:
            recipe_name = recipe.get('name', 'Unknown')
            review_count = db.recipe_reviews.count_documents({'recipe_id': recipe_id})
            
            print(f"✅ Test recipe: {recipe_name}")
            print(f"   Recipe ID: {recipe_id}")
            print(f"   MongoDB _id: {recipe['_id']}")
            print(f"   Reviews: {review_count}")
            
            # Get sample reviews
            reviews = list(db.recipe_reviews.find({'recipe_id': recipe_id}).limit(2))
            print(f"   Sample reviews:")
            for i, review in enumerate(reviews, 1):
                user_name = review.get('user_name')
                rating = review.get('rating')
                text = review.get('review_text', '')[:50]
                print(f"     {i}. {user_name} - {rating}/5 - {text}...")
            
            print(f"\n🔗 To test in UI, search for ingredients and look for recipe: {recipe_name}")
            print(f"   The recipe should show '{review_count} reviews' and clicking should display them.")
        else:
            print("❌ Recipe not found in database")
    else:
        print("❌ No reviews found in database")
    
    client.close()

if __name__ == "__main__":
    print("SisaRasa Review Data Fix")
    print("=" * 50)
    
    response = input("This will clean up invalid review data. Continue? (y/N): ")
    if response.lower() == 'y':
        fix_review_data()
        test_review_api()
    else:
        print("Operation cancelled.")
