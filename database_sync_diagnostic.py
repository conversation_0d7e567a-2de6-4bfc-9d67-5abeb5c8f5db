#!/usr/bin/env python3
"""
Database Synchronization Diagnostic Tool
Comprehensive analysis to identify data synchronization issues between Atlas and local application
"""

import os
import sys
import time
import json
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

class DatabaseSyncDiagnostic:
    def __init__(self):
        self.atlas_uri = os.getenv('MONGO_URI')
        self.local_uri = 'mongodb://localhost:27017/sisarasa'
        self.results = {}
        
    def log(self, message, level="INFO"):
        """Log diagnostic steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_environment_variables(self):
        """Test environment variable configuration."""
        self.log("\n🔍 TESTING ENVIRONMENT VARIABLES")
        self.log("=" * 60)
        
        # Check .env file existence
        env_files = ['.env', '.env.backup', '.env.example']
        for env_file in env_files:
            if os.path.exists(env_file):
                self.log(f"✅ Found {env_file}")
                with open(env_file, 'r') as f:
                    content = f.read()
                    if 'mongodb+srv://' in content:
                        self.log(f"  📍 {env_file} contains Atlas connection string")
                    if 'mongodb://localhost' in content:
                        self.log(f"  📍 {env_file} contains local connection string")
            else:
                self.log(f"❌ Missing {env_file}")
        
        # Check current environment variable
        mongo_uri = os.getenv('MONGO_URI')
        if mongo_uri:
            if 'mongodb+srv://' in mongo_uri:
                self.log(f"✅ MONGO_URI points to Atlas: {mongo_uri[:50]}...")
                self.results['env_config'] = 'atlas'
            elif 'mongodb://localhost' in mongo_uri:
                self.log(f"⚠️  MONGO_URI points to local: {mongo_uri}")
                self.results['env_config'] = 'local'
            else:
                self.log(f"❓ Unknown MONGO_URI format: {mongo_uri}")
                self.results['env_config'] = 'unknown'
        else:
            self.log("❌ MONGO_URI not found in environment")
            self.results['env_config'] = 'missing'
            
    def test_atlas_connection(self):
        """Test connection to Atlas cluster."""
        self.log("\n🌐 TESTING ATLAS CONNECTION")
        self.log("=" * 60)
        
        if not self.atlas_uri or 'mongodb+srv://' not in self.atlas_uri:
            self.log("❌ No valid Atlas URI found", "ERROR")
            self.results['atlas_connection'] = False
            return False
            
        try:
            client = MongoClient(self.atlas_uri, serverSelectionTimeoutMS=10000)
            db = client.get_default_database()
            
            # Test connection
            start_time = time.time()
            client.admin.command('ping')
            connection_time = (time.time() - start_time) * 1000
            
            self.log(f"✅ Atlas connection successful")
            self.log(f"📊 Database: {db.name}")
            self.log(f"⏱️  Connection time: {connection_time:.2f}ms")
            
            # Get collections and document counts
            collections = db.list_collection_names()
            self.log(f"📁 Collections: {len(collections)}")
            
            collection_stats = {}
            for collection_name in collections:
                count = db[collection_name].count_documents({})
                collection_stats[collection_name] = count
                self.log(f"  📄 {collection_name}: {count:,} documents")
            
            self.results['atlas_connection'] = True
            self.results['atlas_collections'] = collection_stats
            self.results['atlas_db_name'] = db.name
            
            client.close()
            return True
            
        except Exception as e:
            self.log(f"❌ Atlas connection failed: {e}", "ERROR")
            self.results['atlas_connection'] = False
            return False
            
    def test_local_connection(self):
        """Test connection to local MongoDB."""
        self.log("\n🏠 TESTING LOCAL CONNECTION")
        self.log("=" * 60)
        
        try:
            client = MongoClient(self.local_uri, serverSelectionTimeoutMS=5000)
            db = client.get_default_database()
            
            # Test connection
            client.admin.command('ping')
            self.log(f"✅ Local connection successful")
            self.log(f"📊 Database: {db.name}")
            
            # Get collections and document counts
            collections = db.list_collection_names()
            self.log(f"📁 Collections: {len(collections)}")
            
            collection_stats = {}
            for collection_name in collections:
                count = db[collection_name].count_documents({})
                collection_stats[collection_name] = count
                self.log(f"  📄 {collection_name}: {count:,} documents")
            
            self.results['local_connection'] = True
            self.results['local_collections'] = collection_stats
            self.results['local_db_name'] = db.name
            
            client.close()
            return True
            
        except Exception as e:
            self.log(f"❌ Local connection failed: {e}", "ERROR")
            self.results['local_connection'] = False
            return False
            
    def test_application_connection(self):
        """Test which database the application is actually using."""
        self.log("\n🔧 TESTING APPLICATION CONNECTION")
        self.log("=" * 60)
        
        try:
            # Import the application's database connection
            sys.path.append('src')
            from api.config import MONGO_URI
            from api.models.user import mongo
            
            self.log(f"📋 Application MONGO_URI: {MONGO_URI[:50]}...")
            
            # Try to access the database through the application
            # This requires the Flask app context
            from api.app import app
            with app.app_context():
                # Test if we can access the database
                try:
                    collections = mongo.db.list_collection_names()
                    self.log(f"✅ Application database accessible")
                    self.log(f"📁 Collections via app: {len(collections)}")
                    
                    # Get some sample data
                    users_count = mongo.db.users.count_documents({})
                    recipes_count = mongo.db.recipes.count_documents({})
                    
                    self.log(f"👥 Users: {users_count:,}")
                    self.log(f"🍽️  Recipes: {recipes_count:,}")
                    
                    self.results['app_connection'] = True
                    self.results['app_users_count'] = users_count
                    self.results['app_recipes_count'] = recipes_count
                    
                except Exception as e:
                    self.log(f"❌ Cannot access database via app: {e}", "ERROR")
                    self.results['app_connection'] = False
                    
        except Exception as e:
            self.log(f"❌ Cannot test application connection: {e}", "ERROR")
            self.results['app_connection'] = False
            
    def test_data_synchronization(self):
        """Test data synchronization by performing operations."""
        self.log("\n🔄 TESTING DATA SYNCHRONIZATION")
        self.log("=" * 60)
        
        if not self.results.get('atlas_connection'):
            self.log("❌ Cannot test sync - Atlas not connected", "ERROR")
            return
            
        try:
            # Connect to Atlas
            atlas_client = MongoClient(self.atlas_uri)
            atlas_db = atlas_client.get_default_database()
            
            # Create a test document
            test_collection = atlas_db['sync_test']
            test_doc = {
                'test_id': 'sync_diagnostic_test',
                'timestamp': datetime.utcnow(),
                'message': 'Testing data synchronization'
            }
            
            self.log("📝 Inserting test document to Atlas...")
            result = test_collection.insert_one(test_doc)
            test_doc_id = result.inserted_id
            self.log(f"✅ Test document inserted: {test_doc_id}")
            
            # Wait a moment
            time.sleep(2)
            
            # Try to read it back
            found_doc = test_collection.find_one({'_id': test_doc_id})
            if found_doc:
                self.log("✅ Test document found in Atlas")
                self.results['sync_test'] = True
            else:
                self.log("❌ Test document not found in Atlas", "ERROR")
                self.results['sync_test'] = False
            
            # Clean up
            test_collection.delete_one({'_id': test_doc_id})
            self.log("🧹 Test document cleaned up")
            
            atlas_client.close()
            
        except Exception as e:
            self.log(f"❌ Sync test failed: {e}", "ERROR")
            self.results['sync_test'] = False
            
    def generate_diagnostic_report(self):
        """Generate comprehensive diagnostic report."""
        self.log("\n📊 DIAGNOSTIC REPORT")
        self.log("=" * 60)
        
        # Environment Configuration
        env_status = self.results.get('env_config', 'unknown')
        if env_status == 'atlas':
            self.log("✅ Environment configured for Atlas")
        elif env_status == 'local':
            self.log("⚠️  Environment configured for LOCAL (this might be the issue!)")
        else:
            self.log("❌ Environment configuration issue")
        
        # Connection Status
        atlas_connected = self.results.get('atlas_connection', False)
        local_connected = self.results.get('local_connection', False)
        app_connected = self.results.get('app_connection', False)
        
        self.log(f"🌐 Atlas Connection: {'✅' if atlas_connected else '❌'}")
        self.log(f"🏠 Local Connection: {'✅' if local_connected else '❌'}")
        self.log(f"🔧 App Connection: {'✅' if app_connected else '❌'}")
        
        # Data Comparison
        if atlas_connected and local_connected:
            atlas_collections = self.results.get('atlas_collections', {})
            local_collections = self.results.get('local_collections', {})
            
            self.log("\n📊 DATA COMPARISON:")
            for collection in set(list(atlas_collections.keys()) + list(local_collections.keys())):
                atlas_count = atlas_collections.get(collection, 0)
                local_count = local_collections.get(collection, 0)
                
                if atlas_count != local_count:
                    self.log(f"⚠️  {collection}: Atlas={atlas_count:,}, Local={local_count:,} (MISMATCH!)")
                else:
                    self.log(f"✅ {collection}: {atlas_count:,} (same in both)")
        
        # Diagnosis
        self.log("\n🔍 DIAGNOSIS:")
        if env_status == 'local' and local_connected:
            self.log("🚨 ISSUE IDENTIFIED: Your application is connecting to LOCAL MongoDB!")
            self.log("   This explains why Atlas deletions don't affect your app.")
            self.log("   Solution: Update your .env file to use the Atlas connection string.")
        elif env_status == 'atlas' and atlas_connected:
            self.log("✅ Configuration appears correct - app should use Atlas")
            if local_connected and atlas_connected:
                self.log("💡 Both databases are accessible - check for caching issues")
        
        return self.results

def main():
    """Run the diagnostic."""
    diagnostic = DatabaseSyncDiagnostic()
    
    diagnostic.log("🔍 DATABASE SYNCHRONIZATION DIAGNOSTIC")
    diagnostic.log("=" * 60)
    diagnostic.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    diagnostic.test_environment_variables()
    diagnostic.test_atlas_connection()
    diagnostic.test_local_connection()
    diagnostic.test_application_connection()
    diagnostic.test_data_synchronization()
    
    # Generate report
    results = diagnostic.generate_diagnostic_report()
    
    # Save results to file
    with open('database_sync_diagnostic_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    diagnostic.log("\n💾 Results saved to: database_sync_diagnostic_results.json")
    
    return results

if __name__ == "__main__":
    main()
