#!/usr/bin/env python3
"""
Real-time Database Synchronization Test
Tests if changes made to Atlas are immediately reflected in the application
"""

import os
import sys
import time
import json
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

class RealTimeSyncTester:
    def __init__(self):
        self.atlas_uri = os.getenv('MONGO_URI')
        self.test_collection_name = 'sync_test_posts'
        
    def log(self, message, level="INFO"):
        """Log test steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_real_time_sync(self):
        """Test real-time synchronization between Atlas and application."""
        self.log("\n🔄 TESTING REAL-TIME SYNCHRONIZATION")
        self.log("=" * 60)
        
        try:
            # Connect to Atlas directly
            atlas_client = MongoClient(self.atlas_uri)
            atlas_db = atlas_client.get_default_database()
            test_collection = atlas_db[self.test_collection_name]
            
            # Test 1: Insert a document via Atlas
            self.log("📝 Test 1: Inserting document via Atlas...")
            test_doc = {
                'title': 'Real-time Sync Test',
                'content': 'Testing if Atlas changes are visible in app',
                'timestamp': datetime.utcnow(),
                'test_id': 'realtime_sync_test'
            }
            
            result = test_collection.insert_one(test_doc)
            test_doc_id = result.inserted_id
            self.log(f"✅ Document inserted with ID: {test_doc_id}")
            
            # Test 2: Try to read it through the application
            self.log("🔍 Test 2: Reading document through application...")
            try:
                # Import the application's database connection
                sys.path.append('src')
                from api.app import app
                from api.models.user import mongo
                
                with app.app_context():
                    # Try to find the document we just inserted
                    app_doc = mongo.db[self.test_collection_name].find_one({'_id': test_doc_id})
                    
                    if app_doc:
                        self.log("✅ Document found through application - SYNC WORKING!")
                        return True
                    else:
                        self.log("❌ Document NOT found through application - SYNC ISSUE!", "ERROR")
                        return False
                        
            except Exception as e:
                self.log(f"❌ Error accessing through application: {e}", "ERROR")
                return False
                
            finally:
                # Clean up
                test_collection.delete_one({'_id': test_doc_id})
                atlas_client.close()
                
        except Exception as e:
            self.log(f"❌ Test failed: {e}", "ERROR")
            return False
            
    def test_community_posts_sync(self):
        """Test synchronization specifically with community_posts collection."""
        self.log("\n📱 TESTING COMMUNITY POSTS SYNCHRONIZATION")
        self.log("=" * 60)
        
        try:
            # Connect to Atlas
            atlas_client = MongoClient(self.atlas_uri)
            atlas_db = atlas_client.get_default_database()
            posts_collection = atlas_db['community_posts']
            
            # Get current count via Atlas
            atlas_count_before = posts_collection.count_documents({})
            self.log(f"📊 Atlas community_posts count (before): {atlas_count_before}")
            
            # Get current count via application
            sys.path.append('src')
            from api.app import app
            from api.models.user import mongo
            
            with app.app_context():
                app_count_before = mongo.db.community_posts.count_documents({})
                self.log(f"📱 App community_posts count (before): {app_count_before}")
                
                if atlas_count_before != app_count_before:
                    self.log("⚠️  MISMATCH DETECTED! Atlas and App have different counts", "WARNING")
                    
                    # Show some sample documents from both
                    self.log("🔍 Sample from Atlas:")
                    atlas_sample = list(posts_collection.find({}).limit(3))
                    for i, doc in enumerate(atlas_sample):
                        self.log(f"  {i+1}. {doc.get('title', 'No title')} (ID: {doc['_id']})")
                    
                    self.log("🔍 Sample from App:")
                    app_sample = list(mongo.db.community_posts.find({}).limit(3))
                    for i, doc in enumerate(app_sample):
                        self.log(f"  {i+1}. {doc.get('title', 'No title')} (ID: {doc['_id']})")
                
            # Insert a test post via Atlas
            self.log("📝 Inserting test post via Atlas...")
            test_post = {
                'title': 'Sync Test Post',
                'content': 'Testing real-time synchronization',
                'author_id': ObjectId(),
                'author_name': 'Sync Tester',
                'created_at': datetime.utcnow(),
                'likes': 0,
                'comments': [],
                'tags': ['test', 'sync'],
                'test_marker': 'realtime_sync_test'
            }
            
            result = posts_collection.insert_one(test_post)
            test_post_id = result.inserted_id
            self.log(f"✅ Test post inserted with ID: {test_post_id}")
            
            # Wait a moment
            time.sleep(1)
            
            # Check if it appears in the application
            with app.app_context():
                app_post = mongo.db.community_posts.find_one({'_id': test_post_id})
                
                if app_post:
                    self.log("✅ Test post found in application - SYNC WORKING!")
                    sync_working = True
                else:
                    self.log("❌ Test post NOT found in application - SYNC ISSUE!", "ERROR")
                    sync_working = False
                    
                # Get updated counts
                atlas_count_after = posts_collection.count_documents({})
                app_count_after = mongo.db.community_posts.count_documents({})
                
                self.log(f"📊 Atlas community_posts count (after): {atlas_count_after}")
                self.log(f"📱 App community_posts count (after): {app_count_after}")
                
            # Clean up
            posts_collection.delete_one({'_id': test_post_id})
            atlas_client.close()
            
            return sync_working
            
        except Exception as e:
            self.log(f"❌ Community posts sync test failed: {e}", "ERROR")
            return False
            
    def test_browser_cache_issue(self):
        """Test if the issue might be browser-side caching."""
        self.log("\n🌐 TESTING BROWSER CACHE SCENARIOS")
        self.log("=" * 60)
        
        # Check if there are any static data files that might be cached
        static_files = [
            'data/clean_recipes.json',
            'src/api/static/',
            'src/api/templates/'
        ]
        
        for file_path in static_files:
            if os.path.exists(file_path):
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    mod_time = datetime.fromtimestamp(stat.st_mtime)
                    self.log(f"📄 {file_path}: Last modified {mod_time}")
                else:
                    self.log(f"📁 {file_path}: Directory exists")
            else:
                self.log(f"❌ {file_path}: Not found")
                
        # Check for localStorage usage in templates
        self.log("\n🔍 Checking for localStorage usage...")
        template_dir = 'src/api/templates'
        if os.path.exists(template_dir):
            for template_file in os.listdir(template_dir):
                if template_file.endswith('.html'):
                    file_path = os.path.join(template_dir, template_file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            if 'localStorage' in content:
                                self.log(f"📱 {template_file}: Uses localStorage")
                            if 'sessionStorage' in content:
                                self.log(f"📱 {template_file}: Uses sessionStorage")
                    except Exception as e:
                        self.log(f"❌ Error reading {template_file}: {e}")
                        
    def generate_sync_report(self):
        """Generate a comprehensive synchronization report."""
        self.log("\n📊 SYNCHRONIZATION ANALYSIS REPORT")
        self.log("=" * 60)
        
        # Run all tests
        realtime_sync = self.test_real_time_sync()
        community_sync = self.test_community_posts_sync()
        self.test_browser_cache_issue()
        
        # Summary
        self.log("\n🎯 SUMMARY:")
        self.log(f"Real-time Sync: {'✅ WORKING' if realtime_sync else '❌ BROKEN'}")
        self.log(f"Community Posts Sync: {'✅ WORKING' if community_sync else '❌ BROKEN'}")
        
        if not realtime_sync or not community_sync:
            self.log("\n🔍 POSSIBLE CAUSES:")
            self.log("1. Application is connecting to local MongoDB instead of Atlas")
            self.log("2. Browser is caching old data (localStorage/sessionStorage)")
            self.log("3. Application server needs restart to pick up new connection")
            self.log("4. Multiple database connections causing confusion")
            
            self.log("\n💡 RECOMMENDED SOLUTIONS:")
            self.log("1. Restart your Flask application server")
            self.log("2. Clear browser cache and localStorage")
            self.log("3. Verify .env file is being loaded correctly")
            self.log("4. Stop local MongoDB service if running")
        
        return {
            'realtime_sync': realtime_sync,
            'community_sync': community_sync,
            'timestamp': datetime.utcnow().isoformat()
        }

def main():
    """Run the synchronization test."""
    tester = RealTimeSyncTester()
    
    tester.log("🔄 REAL-TIME DATABASE SYNCHRONIZATION TEST")
    tester.log("=" * 60)
    tester.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run comprehensive test
    results = tester.generate_sync_report()
    
    # Save results
    with open('realtime_sync_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    tester.log("\n💾 Results saved to: realtime_sync_test_results.json")
    
    return results

if __name__ == "__main__":
    main()
