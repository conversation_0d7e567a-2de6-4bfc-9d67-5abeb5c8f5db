#!/usr/bin/env python3
"""
Direct Database Test for Community Fix
Test the community_posts model directly to verify the database connection fix
"""

import os
import sys
import json
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

# Add src to path to import models
sys.path.append('src')

class DirectDatabaseTester:
    def __init__(self):
        self.mongo_uri = os.getenv('MONGO_URI')
        
    def log(self, message, level="INFO"):
        """Log test steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_environment_variables(self):
        """Test environment variable configuration."""
        self.log("\n🔍 TESTING ENVIRONMENT VARIABLES")
        self.log("=" * 60)
        
        mongo_uri = os.getenv('MONGO_URI')
        mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        
        self.log(f"MONGO_URI: {mongo_uri}")
        self.log(f"MONGODB_URI: {mongodb_uri}")
        
        if mongo_uri and 'mongodb+srv' in mongo_uri:
            self.log("✅ MONGO_URI points to Atlas")
        else:
            self.log("❌ MONGO_URI not configured for Atlas", "ERROR")
            
        return mongo_uri
        
    def test_direct_model_import(self):
        """Test importing the community_posts model directly."""
        self.log("\n📦 TESTING MODEL IMPORT")
        self.log("=" * 60)
        
        try:
            from api.models.community_posts import create_post, get_user_info, client, db
            self.log("✅ Successfully imported community_posts model")
            
            # Test the database connection
            try:
                # Test connection
                client.admin.command('ping')
                self.log("✅ Database connection successful")
                
                # Check which database we're connected to
                db_name = db.name
                self.log(f"Connected to database: {db_name}")
                
                # Count documents
                users_count = db.users.count_documents({})
                posts_count = db.community_posts.count_documents({})
                self.log(f"Database contents: {users_count} users, {posts_count} posts")
                
                return True, db
                
            except Exception as e:
                self.log(f"❌ Database connection failed: {e}", "ERROR")
                return False, None
                
        except Exception as e:
            self.log(f"❌ Model import failed: {e}", "ERROR")
            return False, None
            
    def test_user_info_function(self, db):
        """Test the get_user_info function."""
        self.log("\n👤 TESTING USER INFO FUNCTION")
        self.log("=" * 60)
        
        try:
            from api.models.community_posts import get_user_info
            
            # Get a test user
            user = db.users.find_one({})
            if not user:
                self.log("❌ No users found in database", "ERROR")
                return False
                
            user_id = str(user['_id'])
            user_name = user.get('name', 'Unknown')
            self.log(f"Testing with user: {user_name} (ID: {user_id})")
            
            # Test get_user_info function
            user_info = get_user_info(user_id)
            self.log(f"get_user_info result: {user_info}")
            
            if user_info.get('user_name') != 'Anonymous User':
                self.log("✅ User info retrieved successfully!")
                self.log(f"User Name: {user_info.get('user_name')}")
                self.log(f"User ID: {user_info.get('user_id')}")
                return True, user_id
            else:
                self.log("❌ User info shows 'Anonymous User'", "ERROR")
                return False, user_id
                
        except Exception as e:
            self.log(f"❌ User info test failed: {e}", "ERROR")
            return False, None
            
    def test_post_creation_direct(self, db, user_id):
        """Test creating a post directly using the model."""
        self.log("\n📝 TESTING DIRECT POST CREATION")
        self.log("=" * 60)
        
        try:
            from api.models.community_posts import create_post
            
            # Get initial count
            initial_count = db.community_posts.count_documents({})
            self.log(f"Initial posts count: {initial_count}")
            
            # Create test post
            test_content = f"🧪 Direct test post - {datetime.now().isoformat()}"
            result = create_post(user_id, test_content)
            
            self.log(f"create_post result: {result}")
            
            if result.get('status') == 'success':
                post_data = result.get('post', {})
                self.log("✅ Post created successfully!")
                self.log(f"Post ID: {post_data.get('id')}")
                self.log(f"User Name: {post_data.get('user_name')}")
                self.log(f"Content: {post_data.get('content')}")
                
                # Verify in database
                final_count = db.community_posts.count_documents({})
                self.log(f"Final posts count: {final_count}")
                
                if final_count > initial_count:
                    self.log("✅ Post count increased - post saved to database!")
                    return True, post_data.get('id')
                else:
                    self.log("❌ Post count didn't increase", "ERROR")
                    return False, None
            else:
                self.log(f"❌ Post creation failed: {result.get('message')}", "ERROR")
                return False, None
                
        except Exception as e:
            self.log(f"❌ Direct post creation failed: {e}", "ERROR")
            return False, None
            
    def test_comment_creation_direct(self, db, user_id, post_id):
        """Test creating a comment directly using the model."""
        self.log("\n💬 TESTING DIRECT COMMENT CREATION")
        self.log("=" * 60)
        
        try:
            from api.models.community_posts import create_comment
            
            # Get initial count
            initial_count = db.post_comments.count_documents({})
            self.log(f"Initial comments count: {initial_count}")
            
            # Create test comment
            test_content = f"🧪 Direct test comment - {datetime.now().isoformat()}"
            result = create_comment(post_id, user_id, test_content)
            
            self.log(f"create_comment result: {result}")
            
            if result.get('status') == 'success':
                comment_data = result.get('comment', {})
                self.log("✅ Comment created successfully!")
                self.log(f"Comment ID: {comment_data.get('id')}")
                self.log(f"User Name: {comment_data.get('user_name')}")
                self.log(f"Content: {comment_data.get('content')}")
                
                # Verify in database
                final_count = db.post_comments.count_documents({})
                self.log(f"Final comments count: {final_count}")
                
                if final_count > initial_count:
                    self.log("✅ Comment count increased - comment saved to database!")
                    return True
                else:
                    self.log("❌ Comment count didn't increase", "ERROR")
                    return False
            else:
                self.log(f"❌ Comment creation failed: {result.get('message')}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Direct comment creation failed: {e}", "ERROR")
            return False
            
    def verify_atlas_connection(self):
        """Verify we're actually connected to Atlas."""
        self.log("\n🌐 VERIFYING ATLAS CONNECTION")
        self.log("=" * 60)
        
        try:
            # Connect directly to Atlas
            atlas_client = MongoClient(self.mongo_uri)
            atlas_db = atlas_client.get_default_database()
            
            # Get database info
            db_stats = atlas_db.command("dbStats")
            self.log(f"Database name: {atlas_db.name}")
            self.log(f"Database size: {db_stats.get('dataSize', 0)} bytes")
            
            # Compare with local
            local_client = MongoClient('mongodb://localhost:27017/')
            local_db = local_client['sisarasa']
            
            atlas_posts = atlas_db.community_posts.count_documents({})
            local_posts = local_db.community_posts.count_documents({})
            
            self.log(f"Atlas posts: {atlas_posts}")
            self.log(f"Local posts: {local_posts}")
            
            if atlas_posts != local_posts:
                self.log("✅ Atlas and local have different counts - we're using Atlas!")
            else:
                self.log("⚠️  Atlas and local have same counts - need to verify")
                
            atlas_client.close()
            local_client.close()
            
            return True
            
        except Exception as e:
            self.log(f"❌ Atlas verification failed: {e}", "ERROR")
            return False
            
    def run_comprehensive_test(self):
        """Run all direct database tests."""
        self.log("🔧 DIRECT DATABASE FIX VERIFICATION")
        self.log("=" * 60)
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            'environment_ok': False,
            'model_import_ok': False,
            'user_info_ok': False,
            'post_creation_ok': False,
            'comment_creation_ok': False,
            'atlas_connection_ok': False
        }
        
        # Run tests
        mongo_uri = self.test_environment_variables()
        results['environment_ok'] = bool(mongo_uri)
        
        if results['environment_ok']:
            model_ok, db = self.test_direct_model_import()
            results['model_import_ok'] = model_ok
            
            if model_ok:
                user_info_ok, user_id = self.test_user_info_function(db)
                results['user_info_ok'] = user_info_ok
                
                if user_info_ok and user_id:
                    post_ok, post_id = self.test_post_creation_direct(db, user_id)
                    results['post_creation_ok'] = post_ok
                    
                    if post_ok and post_id:
                        comment_ok = self.test_comment_creation_direct(db, user_id, post_id)
                        results['comment_creation_ok'] = comment_ok
                        
        results['atlas_connection_ok'] = self.verify_atlas_connection()
        
        # Summary
        self.log("\n🎯 DIRECT TEST SUMMARY")
        self.log("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"{test_name.replace('_', ' ').title()}: {status}")
            
        if all(results.values()):
            self.log("\n🎉 ALL DIRECT TESTS PASSED!")
            self.log("✅ Community models are now using Atlas database")
            self.log("✅ User information is being properly retrieved")
            self.log("✅ Posts and comments are being saved correctly")
        else:
            failed_tests = [k for k, v in results.items() if not v]
            self.log(f"\n⚠️  Failed tests: {', '.join(failed_tests)}")
            
        return results

def main():
    """Run the direct database test."""
    tester = DirectDatabaseTester()
    results = tester.run_comprehensive_test()
    
    # Save results
    with open('direct_database_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    tester.log("\n💾 Results saved to: direct_database_test_results.json")
    
    return results

if __name__ == "__main__":
    main()
