#!/usr/bin/env python3
"""
Script to check what user-shared recipes exist in the database.
"""

import os
import sys
from pymongo import MongoClient

def check_user_recipes():
    """Check what user-shared recipes exist in the database."""
    
    try:
        # Connect to MongoDB
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        recipes_collection = db['recipes']
        
        # Find all user-shared recipes
        user_recipes = list(recipes_collection.find({
            'original_id': {'$regex': '^user_'}
        }))
        
        print(f"Found {len(user_recipes)} user-shared recipes in database:")
        print("=" * 60)
        
        for i, recipe in enumerate(user_recipes, 1):
            print(f"\n{i}. Recipe: {recipe.get('name', 'Untitled')}")
            print(f"   ID: {recipe.get('original_id', 'N/A')}")
            print(f"   Submitted by: {recipe.get('submitted_by', 'N/A')}")
            print(f"   Created: {recipe.get('created_at', 'N/A')}")
            
            # Show ingredients
            ingredients = recipe.get('ingredients', [])
            if isinstance(ingredients, str):
                try:
                    import json
                    ingredients = json.loads(ingredients)
                except:
                    ingredients = [ingredients] if ingredients else []
            
            print(f"   Ingredients ({len(ingredients)}): {', '.join(ingredients[:5])}")
            if len(ingredients) > 5:
                print(f"                     ... and {len(ingredients) - 5} more")
            
            # Show approval status if it exists
            if 'approval_status' in recipe:
                print(f"   Approval Status: {recipe.get('approval_status')}")
        
        if len(user_recipes) == 0:
            print("No user-shared recipes found in database.")
            print("This could mean:")
            print("- No users have shared recipes yet")
            print("- Recipes are stored with different field names")
            print("- Database connection issues")
        
        return user_recipes
        
    except Exception as e:
        print(f"Error checking user recipes: {e}")
        return []

if __name__ == "__main__":
    print("Checking User-Shared Recipes in Database...")
    print("=" * 50)
    
    recipes = check_user_recipes()
    
    if recipes:
        print(f"\n✅ Found {len(recipes)} user-shared recipes")
        
        # Test with ingredients from the first recipe
        first_recipe = recipes[0]
        ingredients = first_recipe.get('ingredients', [])
        if isinstance(ingredients, str):
            try:
                import json
                ingredients = json.loads(ingredients)
            except:
                ingredients = [ingredients] if ingredients else []
        
        if ingredients:
            print(f"\n💡 Suggestion: Try searching with ingredients from '{first_recipe.get('name')}':")
            print(f"   Ingredients: {', '.join(ingredients[:3])}")
    else:
        print(f"\n⚠️  No user-shared recipes found in database")
