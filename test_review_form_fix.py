#!/usr/bin/env python3
"""
Test script to verify that the review form fixes are working properly.
This script tests the review submission functionality.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000"

def test_review_submission():
    """Test the review submission functionality"""
    print("🧪 Testing Review Form Fixes")
    print("=" * 50)
    
    # Test data
    test_user = {
        "email": "<EMAIL>",
        "password": "testpass123",
        "name": "Test User"
    }
    
    test_recipe_id = "test-recipe-123"
    test_review = {
        "rating": 5,
        "review_text": "Great recipe! The form fixes work perfectly."
    }
    
    try:
        # 1. Register or login user
        print("1. 🔐 Testing user authentication...")
        
        # Try to register (might fail if user exists)
        register_response = requests.post(f"{BASE_URL}/api/register", json=test_user)
        
        # Login to get token
        login_response = requests.post(f"{BASE_URL}/api/login", json={
            "email": test_user["email"],
            "password": test_user["password"]
        })
        
        if login_response.status_code == 200:
            token = login_response.json().get("access_token")
            print("   ✅ Authentication successful")
        else:
            print(f"   ❌ Authentication failed: {login_response.text}")
            return False
        
        # 2. Test review submission
        print("2. 📝 Testing review submission...")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        review_response = requests.post(
            f"{BASE_URL}/api/recipe/{test_recipe_id}/review",
            json=test_review,
            headers=headers
        )
        
        if review_response.status_code == 200:
            print("   ✅ Review submission successful")
            print(f"   📊 Response: {review_response.json()}")
        else:
            print(f"   ❌ Review submission failed: {review_response.status_code}")
            print(f"   📄 Response: {review_response.text}")
            return False
        
        # 3. Test review retrieval
        print("3. 📖 Testing review retrieval...")
        
        reviews_response = requests.get(f"{BASE_URL}/api/recipe/{test_recipe_id}/reviews")
        
        if reviews_response.status_code == 200:
            reviews_data = reviews_response.json()
            print("   ✅ Review retrieval successful")
            print(f"   📊 Found {len(reviews_data.get('reviews', []))} reviews")
        else:
            print(f"   ❌ Review retrieval failed: {reviews_response.status_code}")
            return False
        
        print("\n🎉 All tests passed! Review form fixes are working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def test_frontend_pages():
    """Test that frontend pages load without JavaScript errors"""
    print("\n🌐 Testing Frontend Pages")
    print("=" * 50)
    
    pages_to_test = [
        "/dashboard",
        "/search-results",
        "/profile",
        "/save-recipe"
    ]
    
    for page in pages_to_test:
        try:
            response = requests.get(f"{BASE_URL}{page}")
            if response.status_code == 200:
                # Check if Vue.js is loaded correctly (not the production version)
                if "vue.global.js" in response.text:
                    print(f"   ✅ {page} - Vue.js development version loaded")
                elif "vue.global.prod.js" in response.text:
                    print(f"   ⚠️  {page} - Still using production Vue.js (may cause errors)")
                else:
                    print(f"   ❓ {page} - Vue.js not detected")
            else:
                print(f"   ❌ {page} - Failed to load (status: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {page} - Error: {e}")

if __name__ == "__main__":
    print("🔧 Review Form Fix Verification")
    print("=" * 60)
    print("This script tests the fixes applied to the review form functionality.")
    print("Make sure the SisaRasa server is running on localhost:5000")
    print()
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    # Run tests
    success = test_review_submission()
    test_frontend_pages()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Review form fixes verification completed successfully!")
    else:
        print("❌ Some issues were detected. Check the output above.")
    print("=" * 60)
