<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login and Search</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { padding: 10px; border: 1px solid #ccc; border-radius: 5px; width: 100%; }
        .btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:disabled { background: #ccc; cursor: not-allowed; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .status { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .ingredient-tag { display: inline-block; background: #007bff; color: white; padding: 5px 10px; margin: 5px; border-radius: 15px; }
        .remove-tag { margin-left: 5px; cursor: pointer; font-weight: bold; }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1>Login and Search Test</h1>
        
        <!-- Step 1: Create Account -->
        <div v-if="currentStep === 'signup'">
            <h3>Step 1: Create Test Account</h3>
            <div class="form-group">
                <label>Name:</label>
                <input v-model="signupData.name" placeholder="Test User">
            </div>
            <div class="form-group">
                <label>Email:</label>
                <input v-model="signupData.email" type="email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input v-model="signupData.password" type="password" placeholder="password123">
            </div>
            <button @click="createAccount" :disabled="loading" class="btn">Create Account</button>
            <button @click="currentStep = 'login'" class="btn btn-warning">Skip to Login</button>
        </div>
        
        <!-- Step 2: Login -->
        <div v-if="currentStep === 'login'">
            <h3>Step 2: Login</h3>
            <div class="form-group">
                <label>Email:</label>
                <input v-model="loginData.email" type="email" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input v-model="loginData.password" type="password" placeholder="password123">
            </div>
            <button @click="login" :disabled="loading" class="btn">Login</button>
            <button @click="currentStep = 'signup'" class="btn btn-warning">Back to Signup</button>
        </div>
        
        <!-- Step 3: Test Search -->
        <div v-if="currentStep === 'search'">
            <h3>Step 3: Test Search Functionality</h3>
            <div class="status success">
                ✅ Successfully logged in as: {{ userName }}
            </div>
            
            <div class="form-group">
                <label>Add Ingredients:</label>
                <input v-model="currentInput" @keydown.enter="addIngredient" placeholder="Type ingredient and press Enter">
            </div>
            
            <div v-if="selectedIngredients.length > 0">
                <h4>Selected Ingredients:</h4>
                <div v-for="(ingredient, index) in selectedIngredients" :key="index" class="ingredient-tag">
                    {{ ingredient }}
                    <span class="remove-tag" @click="removeIngredient(index)">&times;</span>
                </div>
            </div>
            
            <button @click="testSearch" :disabled="selectedIngredients.length === 0 || loading" class="btn">
                Test Search ({{ selectedIngredients.length }} ingredients)
            </button>
            <button @click="testDashboardRedirect" class="btn btn-success">
                Test Dashboard Redirect
            </button>
            <button @click="logout" class="btn btn-warning">Logout</button>
        </div>
        
        <!-- Status Messages -->
        <div v-if="status" :class="['status', status.type]">
            {{ status.message }}
        </div>
        
        <!-- Search Results -->
        <div v-if="searchResults">
            <h4>Search Results:</h4>
            <pre>{{ JSON.stringify(searchResults, null, 2) }}</pre>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    currentStep: 'signup',
                    loading: false,
                    status: null,
                    token: null,
                    userName: null,
                    signupData: {
                        name: 'Test User',
                        email: '<EMAIL>',
                        password: 'password123'
                    },
                    loginData: {
                        email: '<EMAIL>',
                        password: 'password123'
                    },
                    currentInput: '',
                    selectedIngredients: [],
                    searchResults: null
                }
            },
            mounted() {
                // Check if already logged in
                const storedToken = localStorage.getItem('token');
                const storedName = localStorage.getItem('userName');
                if (storedToken && storedName) {
                    this.token = storedToken;
                    this.userName = storedName;
                    this.currentStep = 'search';
                    this.setStatus('info', 'Already logged in from previous session');
                }
            },
            methods: {
                setStatus(type, message) {
                    this.status = { type, message };
                    setTimeout(() => this.status = null, 5000);
                },
                
                async createAccount() {
                    this.loading = true;
                    try {
                        const response = await fetch('http://127.0.0.1:5000/api/auth/signup', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(this.signupData)
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok && data.status === 'success') {
                            this.setStatus('success', 'Account created successfully! Please login.');
                            this.loginData.email = this.signupData.email;
                            this.loginData.password = this.signupData.password;
                            this.currentStep = 'login';
                        } else {
                            this.setStatus('error', data.message || 'Failed to create account');
                        }
                    } catch (err) {
                        this.setStatus('error', `Error: ${err.message}`);
                    } finally {
                        this.loading = false;
                    }
                },
                
                async login() {
                    this.loading = true;
                    try {
                        const response = await fetch('http://127.0.0.1:5000/api/auth/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(this.loginData)
                        });
                        
                        const data = await response.json();
                        
                        if (response.ok && data.status === 'success') {
                            this.token = data.access_token;
                            this.userName = data.user.name;
                            
                            // Store in localStorage
                            localStorage.setItem('token', this.token);
                            localStorage.setItem('userName', this.userName);
                            localStorage.setItem('userId', data.user.id);
                            
                            this.setStatus('success', `Successfully logged in as ${this.userName}`);
                            this.currentStep = 'search';
                        } else {
                            this.setStatus('error', data.message || 'Login failed');
                        }
                    } catch (err) {
                        this.setStatus('error', `Error: ${err.message}`);
                    } finally {
                        this.loading = false;
                    }
                },
                
                logout() {
                    localStorage.removeItem('token');
                    localStorage.removeItem('userName');
                    localStorage.removeItem('userId');
                    this.token = null;
                    this.userName = null;
                    this.currentStep = 'login';
                    this.setStatus('info', 'Logged out successfully');
                },
                
                addIngredient() {
                    const ingredient = this.currentInput.trim().toLowerCase();
                    if (ingredient && !this.selectedIngredients.includes(ingredient)) {
                        this.selectedIngredients.push(ingredient);
                        this.currentInput = '';
                    }
                },
                
                removeIngredient(index) {
                    this.selectedIngredients.splice(index, 1);
                },
                
                async testSearch() {
                    if (this.selectedIngredients.length === 0) return;
                    
                    this.loading = true;
                    this.searchResults = null;
                    
                    try {
                        const response = await fetch('http://127.0.0.1:5000/api/recommend', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${this.token}`
                            },
                            body: JSON.stringify({
                                ingredients: this.selectedIngredients,
                                limit: 5,
                                min_score: 0.01,
                                strict: false
                            })
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        this.searchResults = data;
                        
                        if (data.status === 'ok' && data.recipes && data.recipes.length > 0) {
                            this.setStatus('success', `✅ Search successful! Found ${data.recipes.length} recipes.`);
                        } else {
                            this.setStatus('error', `❌ Search returned no results. Status: ${data.status}`);
                        }
                        
                    } catch (err) {
                        this.setStatus('error', `❌ Search failed: ${err.message}`);
                    } finally {
                        this.loading = false;
                    }
                },
                
                testDashboardRedirect() {
                    // Test the actual dashboard redirect functionality
                    const ingredientsParam = encodeURIComponent(this.selectedIngredients.join(','));
                    const url = `http://127.0.0.1:5000/search-results?ingredients=${ingredientsParam}`;
                    window.open(url, '_blank');
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
