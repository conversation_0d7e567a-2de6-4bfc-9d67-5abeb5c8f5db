#!/usr/bin/env python3
"""
Community Features Issue Diagnostic Tool
Tests community post creation, user authentication, and database persistence
"""

import os
import sys
import json
import requests
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
from bson import ObjectId

# Load environment variables
load_dotenv()

class CommunityIssuesTester:
    def __init__(self):
        self.api_base_url = "http://127.0.0.1:5000"
        self.mongo_uri = os.getenv('MONGO_URI')
        self.test_user_email = "<EMAIL>"
        self.test_user_password = "testpassword123"
        self.auth_token = None
        
    def log(self, message, level="INFO"):
        """Log test steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
        
    def test_database_connections(self):
        """Test different database connection scenarios."""
        self.log("\n🔍 TESTING DATABASE CONNECTIONS")
        self.log("=" * 60)
        
        # Test 1: Check environment variable
        self.log(f"Environment MONGO_URI: {self.mongo_uri}")
        
        # Test 2: Test connection with MONGO_URI
        try:
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            users_count = db.users.count_documents({})
            posts_count = db.community_posts.count_documents({})
            self.log(f"✅ MONGO_URI connection: {users_count} users, {posts_count} posts")
            client.close()
        except Exception as e:
            self.log(f"❌ MONGO_URI connection failed: {e}", "ERROR")
            
        # Test 3: Test connection with MONGODB_URI (what community_posts.py uses)
        mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        self.log(f"Environment MONGODB_URI: {mongodb_uri}")
        
        try:
            client = MongoClient(mongodb_uri)
            db = client['sisarasa']
            users_count = db.users.count_documents({})
            posts_count = db.community_posts.count_documents({})
            self.log(f"✅ MONGODB_URI connection: {users_count} users, {posts_count} posts")
            client.close()
        except Exception as e:
            self.log(f"❌ MONGODB_URI connection failed: {e}", "ERROR")
            
    def test_user_authentication(self):
        """Test user login and JWT token generation."""
        self.log("\n🔐 TESTING USER AUTHENTICATION")
        self.log("=" * 60)
        
        # Test login
        login_data = {
            "email": self.test_user_email,
            "password": self.test_user_password
        }
        
        try:
            response = requests.post(f"{self.api_base_url}/api/auth/login", json=login_data)
            self.log(f"Login response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get('access_token')
                user_info = data.get('user', {})
                self.log(f"✅ Login successful")
                self.log(f"User ID: {user_info.get('id')}")
                self.log(f"User Name: {user_info.get('name')}")
                self.log(f"Token: {self.auth_token[:50]}..." if self.auth_token else "No token")
                return True
            else:
                self.log(f"❌ Login failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Login request failed: {e}", "ERROR")
            return False
            
    def test_community_post_creation(self):
        """Test creating a community post."""
        self.log("\n📝 TESTING COMMUNITY POST CREATION")
        self.log("=" * 60)
        
        if not self.auth_token:
            self.log("❌ No auth token available", "ERROR")
            return False
            
        # Test post creation
        post_data = {
            "content": f"Test post created at {datetime.now().isoformat()}"
        }
        
        headers = {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
        
        try:
            # Get posts count before
            response = requests.get(f"{self.api_base_url}/api/community/posts", headers=headers)
            posts_before = len(response.json()) if response.status_code == 200 else 0
            self.log(f"Posts before creation: {posts_before}")
            
            # Create post
            response = requests.post(f"{self.api_base_url}/api/community/posts", 
                                   json=post_data, headers=headers)
            self.log(f"Post creation response status: {response.status_code}")
            self.log(f"Post creation response: {response.text}")
            
            if response.status_code == 200:
                post_response = response.json()
                self.log(f"✅ Post created successfully")
                self.log(f"Post ID: {post_response.get('id')}")
                self.log(f"User Name: {post_response.get('user_name')}")
                self.log(f"Content: {post_response.get('content')}")
                
                # Verify in database
                return self.verify_post_in_database(post_response.get('id'))
            else:
                self.log(f"❌ Post creation failed: {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Post creation request failed: {e}", "ERROR")
            return False
            
    def verify_post_in_database(self, post_id):
        """Verify that the post was actually saved to the database."""
        self.log("\n🔍 VERIFYING POST IN DATABASE")
        self.log("=" * 60)
        
        try:
            # Check with MONGO_URI (correct one)
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            post = db.community_posts.find_one({'_id': post_id})
            
            if post:
                self.log(f"✅ Post found in Atlas database (MONGO_URI)")
                self.log(f"Post content: {post.get('content')}")
                self.log(f"User ID: {post.get('user_id')}")
                self.log(f"Created at: {post.get('created_at')}")
                atlas_found = True
            else:
                self.log(f"❌ Post NOT found in Atlas database (MONGO_URI)", "ERROR")
                atlas_found = False
            client.close()
            
            # Check with MONGODB_URI (what community_posts.py uses)
            mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
            client = MongoClient(mongodb_uri)
            db = client['sisarasa']
            post = db.community_posts.find_one({'_id': post_id})
            
            if post:
                self.log(f"✅ Post found in community_posts database (MONGODB_URI)")
                self.log(f"Post content: {post.get('content')}")
                self.log(f"User ID: {post.get('user_id')}")
                self.log(f"Created at: {post.get('created_at')}")
                community_found = True
            else:
                self.log(f"❌ Post NOT found in community_posts database (MONGODB_URI)", "ERROR")
                community_found = False
            client.close()
            
            return atlas_found or community_found
            
        except Exception as e:
            self.log(f"❌ Database verification failed: {e}", "ERROR")
            return False
            
    def test_user_info_retrieval(self):
        """Test user info retrieval from different databases."""
        self.log("\n👤 TESTING USER INFO RETRIEVAL")
        self.log("=" * 60)
        
        # Get a test user ID
        try:
            # From Atlas (MONGO_URI)
            client = MongoClient(self.mongo_uri)
            db = client.get_default_database()
            user = db.users.find_one({'email': self.test_user_email})
            
            if user:
                user_id = str(user['_id'])
                self.log(f"✅ Test user found in Atlas: {user.get('name')} (ID: {user_id})")
                
                # Test get_user_info function behavior
                # Simulate what community_posts.py does
                mongodb_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
                community_client = MongoClient(mongodb_uri)
                community_db = community_client['sisarasa']
                community_user = community_db.users.find_one({"_id": ObjectId(user_id)})
                
                if community_user:
                    self.log(f"✅ User found in community database: {community_user.get('name')}")
                else:
                    self.log(f"❌ User NOT found in community database", "ERROR")
                    self.log("This explains why posts show 'Anonymous User'!")
                    
                community_client.close()
            else:
                self.log(f"❌ Test user not found in Atlas", "ERROR")
                
            client.close()
            
        except Exception as e:
            self.log(f"❌ User info test failed: {e}", "ERROR")
            
    def run_comprehensive_test(self):
        """Run all tests to diagnose community issues."""
        self.log("🔍 COMMUNITY FEATURES DIAGNOSTIC TEST")
        self.log("=" * 60)
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {
            'database_connections': False,
            'user_authentication': False,
            'post_creation': False,
            'database_persistence': False,
            'user_info_retrieval': False
        }
        
        # Run tests
        self.test_database_connections()
        results['user_authentication'] = self.test_user_authentication()
        results['post_creation'] = self.test_community_post_creation()
        self.test_user_info_retrieval()
        
        # Summary
        self.log("\n🎯 DIAGNOSTIC SUMMARY")
        self.log("=" * 60)
        
        for test_name, result in results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            self.log(f"{test_name.replace('_', ' ').title()}: {status}")
            
        # Identify root causes
        self.log("\n🔍 ROOT CAUSE ANALYSIS")
        self.log("=" * 60)
        
        self.log("1. DATABASE CONNECTION MISMATCH:")
        self.log("   - Application uses MONGO_URI (Atlas)")
        self.log("   - community_posts.py uses MONGODB_URI (defaults to local)")
        self.log("   - This causes posts to be saved to wrong database")
        
        self.log("2. USER INFO RETRIEVAL ISSUE:")
        self.log("   - get_user_info() looks in wrong database")
        self.log("   - Results in 'Anonymous User' display")
        
        self.log("\n💡 RECOMMENDED FIXES:")
        self.log("1. Fix community_posts.py to use MONGO_URI instead of MONGODB_URI")
        self.log("2. Ensure all models use the same database connection")
        self.log("3. Test post creation after fix")
        
        return results

def main():
    """Run the community issues diagnostic."""
    tester = CommunityIssuesTester()
    results = tester.run_comprehensive_test()
    
    # Save results
    with open('community_issues_diagnostic.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    tester.log("\n💾 Results saved to: community_issues_diagnostic.json")
    
    return results

if __name__ == "__main__":
    main()
