#!/usr/bin/env python3
"""
Test script to verify community recipe fixes with authentication.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def create_test_user_and_login():
    """Create a test user and login to get a token."""
    print("Creating test user and logging in...")
    
    # Test user data with timestamp to ensure uniqueness
    import time
    timestamp = str(int(time.time()))
    test_user = {
        "name": "Test User",
        "email": f"test{timestamp}@example.com",
        "password": "testpassword123"
    }
    
    try:
        # Try to register the user
        register_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
        print(f"Register response: {register_response.status_code}")

        # Try to login (whether registration succeeded or user already exists)
        login_data = {
            "email": test_user["email"],
            "password": test_user["password"]
        }

        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"Login response: {login_response.status_code}")
        
        if login_response.status_code == 200:
            token = login_response.json().get('token')
            if token:
                print("✓ Successfully logged in and got token")
                return token
            else:
                print("✗ Login successful but no token received")
                return None
        else:
            print(f"✗ Login failed: {login_response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error during authentication: {e}")
        return None

def test_shared_recipes_with_auth(token):
    """Test the shared recipes endpoint with authentication."""
    print("\nTesting shared recipes endpoint with authentication...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Got response with {len(data)} recipes")
            
            if data:
                first_recipe = data[0]
                required_fields = ['prep_time', 'cook_time', 'servings', 'difficulty', 'user_id']
                missing_fields = [field for field in required_fields if field not in first_recipe]
                
                if missing_fields:
                    print(f"✗ Missing metadata fields: {missing_fields}")
                    print(f"Available fields: {list(first_recipe.keys())}")
                    return False
                else:
                    print("✓ All metadata fields present in recipe response")
                    print(f"Sample recipe metadata:")
                    for field in required_fields:
                        print(f"  {field}: {first_recipe[field]}")
                    return True
            else:
                print("✓ No recipes found (empty response)")
                return True
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing endpoint: {e}")
        return False

def submit_test_recipe(token):
    """Submit a test recipe to verify the submission works with metadata."""
    print("\nSubmitting test recipe...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    test_recipe = {
        "name": "Test Recipe for Community",
        "description": "A test recipe to verify metadata fields",
        "ingredients": ["1 cup flour", "2 eggs", "1 cup milk"],
        "instructions": ["Mix ingredients", "Cook for 10 minutes"],
        "prep_time": 15,
        "cook_time": 25,
        "servings": 4,
        "difficulty": "Easy",
        "cuisine": "Test Cuisine"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/submit", json=test_recipe, headers=headers)
        print(f"Submit recipe response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Recipe submitted successfully")
            return result.get('recipe_id')
        else:
            print(f"✗ Recipe submission failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error submitting recipe: {e}")
        return None

def main():
    """Run all tests."""
    print("=== Community Recipe Fixes Test with Authentication ===\n")
    
    # Get authentication token
    token = create_test_user_and_login()
    if not token:
        print("✗ Could not authenticate, skipping further tests")
        return 1
    
    # Test shared recipes endpoint
    recipes_test = test_shared_recipes_with_auth(token)
    
    # Submit a test recipe
    recipe_id = submit_test_recipe(token)
    
    # Test shared recipes again to see if our recipe appears
    if recipe_id:
        print("\nTesting shared recipes after submission...")
        recipes_test_after = test_shared_recipes_with_auth(token)
    else:
        recipes_test_after = True  # Skip if submission failed
    
    print(f"\n=== Test Results ===")
    tests_passed = sum([recipes_test, recipes_test_after, recipe_id is not None])
    total_tests = 3
    print(f"Passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed!")
        return 0
    else:
        print("✗ Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
