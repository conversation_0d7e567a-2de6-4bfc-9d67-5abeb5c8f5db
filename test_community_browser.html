<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Page Test</title>
</head>
<body>
    <h1>Community Page Authentication Test</h1>
    <div id="status">Testing...</div>
    <div id="results"></div>
    
    <script>
        // Set the token from our test
        const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8BqbQgCs7jLD_N3ncbf-KHHat8diw7xnOuF6bE6FW9c';
        
        // Store token in localStorage
        localStorage.setItem('token', token);
        
        // Test the authentication flow
        async function testAuth() {
            const statusDiv = document.getElementById('status');
            const resultsDiv = document.getElementById('results');
            
            try {
                statusDiv.textContent = 'Testing /api/auth/me endpoint...';
                
                const response = await fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const userData = await response.json();
                    if (userData.status === 'success' && userData.user) {
                        statusDiv.textContent = '✅ Authentication successful!';
                        resultsDiv.innerHTML = `
                            <h3>User Data:</h3>
                            <p><strong>Name:</strong> ${userData.user.name}</p>
                            <p><strong>Email:</strong> ${userData.user.email}</p>
                            <p><strong>ID:</strong> ${userData.user.id}</p>
                            <p><a href="/community" target="_blank">🔗 Test Community Page</a></p>
                        `;
                    } else {
                        throw new Error('Invalid response format');
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                statusDiv.textContent = '❌ Authentication failed!';
                resultsDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testAuth();
    </script>
</body>
</html>
