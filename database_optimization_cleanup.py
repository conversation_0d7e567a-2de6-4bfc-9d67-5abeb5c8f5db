#!/usr/bin/env python3
"""
Database Optimization and Cleanup Script for SisaRasa System

This comprehensive script optimizes the database by:
1. Removing unused backup collections
2. Cleaning testing and synthetic data
3. Replacing non-Malaysian names with Malaysian names
4. Balancing data distribution
5. Fixing data integrity issues
6. Maintaining system functionality
"""

import os
import sys
import json
import random
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
from bson import ObjectId
import re

# Malaysian names for replacement
MALAYSIAN_MALE_NAMES = [
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", 
    "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"
]

<PERSON><PERSON>Y<PERSON>AN_FEMALE_NAMES = [
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "Normah", "Rohayu", "Suraya", "Wan", "Zarina"
]

MALAYSIAN_SURNAMES = [
    "Abdullah", "Ahmad", "Ali", "Hassan", "Hussein", "Ibrahim", "Ismail",
    "Mohamed", "Omar", "Rahman", "Salleh", "Yusof", "Azman", "Bakar",
    "Daud", "Ghani", "Hamid", "Jaafar", "Kassim", "Mahmud", "Nasir",
    "Osman", "Razak", "Rosli", "Sulaiman", "Taib", "Wahab", "Yaacob",
    "Zakaria", "Zulkifli"
]

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def backup_critical_collections(db):
    """Create backups of critical collections before cleanup."""
    print("\n📦 Creating backups of critical collections...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    critical_collections = ['users', 'recipes', 'community_posts', 'recipe_reviews']
    
    for collection_name in critical_collections:
        if collection_name in db.list_collection_names():
            collection = db[collection_name]
            count = collection.count_documents({})
            if count > 0:
                backup_name = f"{collection_name}_backup_optimization_{timestamp}"
                backup_collection = db[backup_name]
                
                # Copy all documents
                documents = list(collection.find())
                if documents:
                    backup_collection.insert_many(documents)
                    print(f"  📦 Backed up {count} documents from {collection_name} to {backup_name}")
    
    print("✅ Backup completed")

def remove_unused_collections(db):
    """Remove unused backup collections and identify unused collections."""
    print("\n🗑️  Removing unused collections...")
    
    all_collections = db.list_collection_names()
    
    # Collections that are actively used by the system
    active_collections = {
        'users', 'recipes', 'community_posts', 'post_comments', 'post_likes', 
        'comment_likes', 'recipe_reviews', 'review_votes', 'shared_recipes'
    }
    
    # Collections that might be unused (need verification)
    potentially_unused = {
        'recipe_verifications', 'recipe_comments', 'community_comments', 'recipe_likes'
    }
    
    removed_count = 0
    
    # Remove backup collections
    for collection_name in all_collections:
        if 'backup' in collection_name.lower():
            # Check if it's an old backup (more than 7 days old)
            try:
                # Extract timestamp from backup name
                if '_backup_' in collection_name:
                    timestamp_part = collection_name.split('_backup_')[-1]
                    if len(timestamp_part) >= 8:  # YYYYMMDD format
                        backup_date = datetime.strptime(timestamp_part[:8], '%Y%m%d')
                        if (datetime.now() - backup_date).days > 7:
                            db.drop_collection(collection_name)
                            print(f"  🗑️  Removed old backup collection: {collection_name}")
                            removed_count += 1
                        else:
                            print(f"  ⏳ Keeping recent backup: {collection_name}")
            except:
                # If we can't parse the date, ask for confirmation
                print(f"  ❓ Found backup collection with unclear date: {collection_name}")
    
    # Check potentially unused collections
    for collection_name in potentially_unused:
        if collection_name in all_collections:
            collection = db[collection_name]
            count = collection.count_documents({})
            print(f"  📊 {collection_name}: {count} documents")
            
            # For now, we'll keep them but note they might be unused
            if count == 0:
                print(f"  ⚠️  {collection_name} is empty - consider removing")
    
    print(f"✅ Removed {removed_count} unused collections")
    return removed_count

def generate_malaysian_name():
    """Generate a realistic Malaysian name."""
    # Randomly choose gender
    is_male = random.choice([True, False])
    
    if is_male:
        first_name = random.choice(MALAYSIAN_MALE_NAMES)
    else:
        first_name = random.choice(MALAYSIAN_FEMALE_NAMES)
    
    # Sometimes add a surname
    if random.random() < 0.7:  # 70% chance of having surname
        surname = random.choice(MALAYSIAN_SURNAMES)
        return f"{first_name} {surname}"
    else:
        return first_name

def is_likely_synthetic_name(name):
    """Identify if a name is likely synthetic/testing data."""
    if not name or not isinstance(name, str):
        return True
    
    name_lower = name.lower()
    
    # Common test patterns
    test_patterns = [
        'test', 'user', 'admin', 'demo', 'sample', 'fake', 'dummy',
        'lorem', 'ipsum', 'placeholder', 'example', 'temp', 'debug'
    ]
    
    # Check for test patterns
    for pattern in test_patterns:
        if pattern in name_lower:
            return True
    
    # Check for obviously fake patterns
    if re.search(r'\d{3,}', name):  # Names with 3+ consecutive digits
        return True
    
    if len(name) < 2 or len(name) > 50:  # Too short or too long
        return True
    
    # Check for non-human patterns
    if re.search(r'^[a-z]+\d+$', name_lower):  # Like "user123"
        return True
    
    return False

def clean_synthetic_user_data(db):
    """Clean synthetic and testing user data."""
    print("\n🧹 Cleaning synthetic user data...")
    
    users_collection = db['users']
    users = list(users_collection.find())
    
    updated_count = 0
    removed_count = 0
    
    for user in users:
        user_id = user['_id']
        name = user.get('name', '')
        email = user.get('email', '')
        
        # Check if this is clearly test data
        is_test_user = False
        
        # Check email patterns
        if email:
            email_lower = email.lower()
            test_email_patterns = ['test', 'fake', 'dummy', 'example', 'temp']
            if any(pattern in email_lower for pattern in test_email_patterns):
                is_test_user = True
        
        # Check if user has any real activity
        has_activity = False
        
        # Check for saved recipes
        if user.get('saved_recipes') and len(user['saved_recipes']) > 0:
            has_activity = True
        
        # Check for reviews
        review_count = db['recipe_reviews'].count_documents({'user_id': str(user_id)})
        if review_count > 0:
            has_activity = True
        
        # Check for community posts
        post_count = db['community_posts'].count_documents({'user_id': str(user_id)})
        if post_count > 0:
            has_activity = True
        
        # Decision logic
        if is_test_user and not has_activity:
            # Remove completely fake users with no activity
            users_collection.delete_one({'_id': user_id})
            removed_count += 1
            print(f"  🗑️  Removed test user: {email}")
        elif is_likely_synthetic_name(name):
            # Replace synthetic names with Malaysian names
            new_name = generate_malaysian_name()
            users_collection.update_one(
                {'_id': user_id},
                {'$set': {'name': new_name, 'updated_at': datetime.utcnow()}}
            )
            updated_count += 1
            print(f"  🔄 Updated name: '{name}' → '{new_name}'")
    
    print(f"✅ Updated {updated_count} user names, removed {removed_count} test users")
    return updated_count, removed_count

def fix_data_integrity_issues(db):
    """Fix data integrity issues identified in the analysis."""
    print("\n🔧 Fixing data integrity issues...")

    recipes_collection = db['recipes']
    users_collection = db['users']
    posts_collection = db['community_posts']

    fixed_recipes = 0
    removed_recipes = 0
    fixed_posts = 0

    # Fix recipes with missing submitted_by field
    recipes_without_submitter = recipes_collection.find({
        'is_user_submitted': True,
        'submitted_by': {'$exists': False}
    })

    for recipe in recipes_without_submitter:
        recipe_id = recipe['_id']

        # Try to find a valid user to assign
        if 'submitter_id' in recipe:
            submitter_id = recipe['submitter_id']
            user_exists = users_collection.find_one({'_id': ObjectId(submitter_id)})
            if user_exists:
                recipes_collection.update_one(
                    {'_id': recipe_id},
                    {'$set': {'submitted_by': submitter_id}}
                )
                fixed_recipes += 1
                print(f"  🔧 Fixed recipe {recipe_id}: added submitted_by field")
            else:
                # Remove recipe with invalid submitter
                recipes_collection.delete_one({'_id': recipe_id})
                removed_recipes += 1
                print(f"  🗑️  Removed recipe {recipe_id}: invalid submitter")
        else:
            # Remove recipe without proper user reference
            recipes_collection.delete_one({'_id': recipe_id})
            removed_recipes += 1
            print(f"  🗑️  Removed recipe {recipe_id}: no user reference")

    # Remove duplicate posts
    pipeline = [
        {'$group': {
            '_id': '$content',
            'ids': {'$push': '$_id'},
            'count': {'$sum': 1}
        }},
        {'$match': {'count': {'$gt': 1}}}
    ]

    duplicates = list(posts_collection.aggregate(pipeline))
    for duplicate in duplicates:
        ids_to_remove = duplicate['ids'][1:]  # Keep the first one
        posts_collection.delete_many({'_id': {'$in': ids_to_remove}})
        fixed_posts += len(ids_to_remove)
        print(f"  🔧 Removed {len(ids_to_remove)} duplicate posts")

    print(f"✅ Fixed {fixed_recipes} recipes, removed {removed_recipes} invalid recipes")
    print(f"✅ Fixed {fixed_posts} duplicate posts")
    return fixed_recipes, removed_recipes, fixed_posts

def clean_orphaned_data(db):
    """Remove orphaned comments and likes."""
    print("\n🧹 Cleaning orphaned data...")

    # Clean orphaned post comments
    post_comments = db['post_comments']
    posts = db['community_posts']

    orphaned_comments = 0
    for comment in post_comments.find():
        post_id = comment.get('post_id')
        if post_id:
            post_exists = posts.find_one({'_id': post_id})
            if not post_exists:
                post_comments.delete_one({'_id': comment['_id']})
                orphaned_comments += 1

    # Clean orphaned post likes
    post_likes = db['post_likes']
    orphaned_likes = 0
    for like in post_likes.find():
        post_id = like.get('post_id')
        if post_id:
            post_exists = posts.find_one({'_id': post_id})
            if not post_exists:
                post_likes.delete_one({'_id': like['_id']})
                orphaned_likes += 1

    # Clean orphaned comment likes
    comment_likes = db['comment_likes']
    orphaned_comment_likes = 0
    for like in comment_likes.find():
        comment_id = like.get('comment_id')
        if comment_id:
            comment_exists = post_comments.find_one({'_id': comment_id})
            if not comment_exists:
                comment_likes.delete_one({'_id': like['_id']})
                orphaned_comment_likes += 1

    print(f"✅ Removed {orphaned_comments} orphaned comments")
    print(f"✅ Removed {orphaned_likes} orphaned post likes")
    print(f"✅ Removed {orphaned_comment_likes} orphaned comment likes")
    return orphaned_comments, orphaned_likes, orphaned_comment_likes

def optimize_data_distribution(db):
    """Optimize data distribution across content types."""
    print("\n📊 Optimizing data distribution...")

    # Get current distribution
    users_count = db['users'].count_documents({})
    recipes_count = db['recipes'].count_documents({})
    posts_count = db['community_posts'].count_documents({})
    reviews_count = db['recipe_reviews'].count_documents({})
    comments_count = db['post_comments'].count_documents({})

    print(f"  📈 Current distribution:")
    print(f"    Users: {users_count}")
    print(f"    Recipes: {recipes_count}")
    print(f"    Posts: {posts_count}")
    print(f"    Reviews: {reviews_count}")
    print(f"    Comments: {comments_count}")

    # Remove excessive synthetic reviews if there are too many
    if reviews_count > users_count * 5:  # More than 5 reviews per user on average
        print("  ⚠️  Excessive reviews detected, cleaning up...")

        # Keep only the most recent reviews for each user
        pipeline = [
            {'$group': {
                '_id': '$user_id',
                'reviews': {'$push': {'id': '$_id', 'created_at': '$created_at'}},
                'count': {'$sum': 1}
            }},
            {'$match': {'count': {'$gt': 3}}}  # Users with more than 3 reviews
        ]

        users_with_many_reviews = list(db['recipe_reviews'].aggregate(pipeline))
        removed_reviews = 0

        for user_data in users_with_many_reviews:
            reviews = sorted(user_data['reviews'], key=lambda x: x['created_at'], reverse=True)
            reviews_to_remove = reviews[3:]  # Keep only 3 most recent

            review_ids = [r['id'] for r in reviews_to_remove]
            db['recipe_reviews'].delete_many({'_id': {'$in': review_ids}})
            removed_reviews += len(review_ids)

        print(f"  🗑️  Removed {removed_reviews} excessive reviews")

    return True

def main():
    """Main optimization function."""
    print("🚀 SisaRasa Database Optimization & Cleanup")
    print("=" * 60)
    print(f"Optimization started at: {datetime.now().isoformat()}")

    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return

    try:
        # Step 1: Create backups
        backup_critical_collections(db)

        # Step 2: Remove unused collections
        removed_collections = remove_unused_collections(db)

        # Step 3: Clean synthetic user data
        updated_users, removed_users = clean_synthetic_user_data(db)

        # Step 4: Fix data integrity issues
        fixed_recipes, removed_recipes, fixed_posts = fix_data_integrity_issues(db)

        # Step 5: Clean orphaned data
        orphaned_comments, orphaned_likes, orphaned_comment_likes = clean_orphaned_data(db)

        # Step 6: Optimize data distribution
        optimize_data_distribution(db)

        # Summary
        print(f"\n📋 OPTIMIZATION SUMMARY")
        print("=" * 40)
        print(f"Collections removed: {removed_collections}")
        print(f"User names updated: {updated_users}")
        print(f"Test users removed: {removed_users}")
        print(f"Recipes fixed: {fixed_recipes}")
        print(f"Invalid recipes removed: {removed_recipes}")
        print(f"Duplicate posts fixed: {fixed_posts}")
        print(f"Orphaned data cleaned: {orphaned_comments + orphaned_likes + orphaned_comment_likes}")
        print(f"Optimization completed at: {datetime.now().isoformat()}")

    except Exception as e:
        print(f"❌ Error during optimization: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
