#!/usr/bin/env python3
"""
Debug script to test profile display issues in recipe sharing functionality.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5000"

def test_login():
    """Test login and get auth token"""
    print("Testing login...")
    
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            print(f"✅ Login successful for user: {data['user']['name']}")
            return data['token']
        else:
            print(f"❌ Login failed: {data.get('message')}")
            return None
    else:
        print(f"❌ Login request failed with status {response.status_code}")
        return None

def test_shared_recipes_data(token):
    """Test the shared recipes endpoint to see what user data is returned"""
    print("\n🔍 Testing shared recipes data structure...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
    
    if response.status_code == 200:
        recipes = response.json()
        print(f"✅ Found {len(recipes)} shared recipes")
        
        if recipes:
            recipe = recipes[0]  # Check first recipe
            print(f"\n📋 First recipe data structure:")
            print(f"   - Recipe name: {recipe.get('name', 'N/A')}")
            print(f"   - Recipe ID: {recipe.get('id', 'N/A')}")
            
            # Check user profile fields
            print(f"\n👤 User profile fields:")
            print(f"   - user_name: {recipe.get('user_name', 'MISSING')}")
            print(f"   - username: {recipe.get('username', 'MISSING')}")
            print(f"   - user_profile_image: {recipe.get('user_profile_image', 'MISSING')}")
            print(f"   - profile_picture: {recipe.get('profile_picture', 'MISSING')}")
            print(f"   - user_id: {recipe.get('user_id', 'MISSING')}")
            
            return recipe.get('id')
        else:
            print("❌ No recipes found")
            return None
    else:
        print(f"❌ Shared recipes request failed with status {response.status_code}")
        return None

def test_recipe_comments_data(token, recipe_id):
    """Test the recipe comments endpoint to see what user data is returned"""
    print(f"\n🔍 Testing recipe comments data for recipe {recipe_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/community/recipe/{recipe_id}/comments", headers=headers)
    
    if response.status_code == 200:
        comments = response.json()
        print(f"✅ Found {len(comments)} comments")
        
        if comments:
            comment = comments[0]  # Check first comment
            print(f"\n💬 First comment data structure:")
            print(f"   - Comment ID: {comment.get('id', 'N/A')}")
            print(f"   - Content: {comment.get('content', 'N/A')[:50]}...")
            
            # Check user profile fields
            print(f"\n👤 Comment author profile fields:")
            print(f"   - user_name: {comment.get('user_name', 'MISSING')}")
            print(f"   - username: {comment.get('username', 'MISSING')}")
            print(f"   - user_profile_image: {comment.get('user_profile_image', 'MISSING')}")
            print(f"   - profile_picture: {comment.get('profile_picture', 'MISSING')}")
            print(f"   - user_id: {comment.get('user_id', 'MISSING')}")
            
        else:
            print("ℹ️  No comments found for this recipe")
    else:
        print(f"❌ Recipe comments request failed with status {response.status_code}")

def test_specific_recipe_data(token, recipe_id):
    """Test getting a specific recipe by ID"""
    print(f"\n🔍 Testing specific recipe data for recipe {recipe_id}...")
    
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/shared-recipes/{recipe_id}", headers=headers)
    
    if response.status_code == 200:
        recipe = response.json()
        print(f"✅ Recipe details loaded")
        
        print(f"\n📋 Recipe owner data structure:")
        print(f"   - Recipe name: {recipe.get('name', 'N/A')}")
        
        # Check user profile fields
        print(f"\n👤 Recipe owner profile fields:")
        print(f"   - user_name: {recipe.get('user_name', 'MISSING')}")
        print(f"   - username: {recipe.get('username', 'MISSING')}")
        print(f"   - user_profile_image: {recipe.get('user_profile_image', 'MISSING')}")
        print(f"   - profile_picture: {recipe.get('profile_picture', 'MISSING')}")
        print(f"   - user_id: {recipe.get('user_id', 'MISSING')}")
        
    else:
        print(f"❌ Specific recipe request failed with status {response.status_code}")

def main():
    """Run all debug tests"""
    print("🔍 Debug: Profile Display Issues in Recipe Sharing")
    print("=" * 60)
    
    # Test 1: Login and get token
    token = test_login()
    if not token:
        print("\n❌ Cannot proceed without authentication token")
        sys.exit(1)
    
    # Test 2: Check shared recipes data structure
    recipe_id = test_shared_recipes_data(token)
    
    if recipe_id:
        # Test 3: Check specific recipe data structure
        test_specific_recipe_data(token, recipe_id)
        
        # Test 4: Check recipe comments data structure
        test_recipe_comments_data(token, recipe_id)
    
    print("\n" + "=" * 60)
    print("🎯 Key Findings:")
    print("   - Check if user profile fields are consistently named")
    print("   - Verify frontend template uses correct field names")
    print("   - Ensure backend returns all required user profile data")

if __name__ == "__main__":
    main()
