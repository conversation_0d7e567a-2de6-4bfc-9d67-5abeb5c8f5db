# SisaRasa Synthetic Data Generation Guide

This guide explains how to generate realistic synthetic data for your SisaRasa recipe recommendation system to demonstrate the effectiveness of KNN and hybrid recommendation algorithms.

## 🎯 Purpose

The synthetic data generator creates realistic user interactions, reviews, search patterns, and community engagement to:

- **Showcase KNN Algorithm**: Provide diverse ingredient search patterns and user preferences
- **Enable Collaborative Filtering**: Generate user ratings and reviews for recipe recommendations
- **Support Content-Based Filtering**: Create user profiles with cuisine preferences and dietary restrictions
- **Demonstrate Analytics**: Populate user engagement metrics and activity patterns
- **Test Community Features**: Generate review votes and user interactions

## 📊 Data Generated

### 1. **User Accounts (50-100 users)**
- **Personas**: 6 different user types with realistic behaviors:
  - `casual_cook`: Low activity, basic preferences
  - `food_enthusiast`: High activity, diverse cuisine interests
  - `health_conscious`: Medium activity, dietary restrictions
  - `busy_parent`: Medium activity, quick meal preferences
  - `vegan_lifestyle`: High activity, plant-based focus
  - `college_student`: Medium activity, budget-friendly meals

- **User Data**:
  - Realistic names and email addresses
  - Registration dates spread over 2 years
  - Dietary restrictions and preferences
  - Profile analytics and engagement metrics

### 2. **Recipe Reviews & Ratings**
- **Rating Distribution**: Realistic 5-star ratings (more 4-5 stars, fewer 1-2 stars)
- **Review Text**: Authentic review comments matching rating sentiment
- **Review Patterns**: 
  - Advanced cooks are more critical
  - Beginners are more forgiving
  - ~70% of recipes have reviews (not all)
  - 1-10 reviews per recipe (weighted towards fewer)

### 3. **Search History & Patterns**
- **Realistic Combinations**: Common ingredient pairings like "chicken, rice, vegetables"
- **Persona-Based Searches**: Users search based on their cooking skill and preferences
- **Frequency Patterns**:
  - High-activity users: 15-40 searches
  - Medium-activity users: 8-20 searches
  - Low-activity users: 2-10 searches
- **Temporal Distribution**: Searches spread over user's registration period

### 4. **Saved Recipe Collections**
- **Preference-Based Saving**: 70% of saves match user's cuisine preferences
- **Realistic Volumes**:
  - Power users: 10-30 saved recipes
  - Medium users: 5-15 saved recipes
  - Casual users: 1-8 saved recipes

### 5. **Community Interactions**
- **Review Votes**: Helpful/unhelpful votes on reviews
- **Vote Patterns**: Good reviews (4-5 stars) get more helpful votes
- **Engagement**: ~60% of reviews receive votes

### 6. **User Analytics**
- **Activity Metrics**: Recipe views, saves, reviews given
- **Cooking Streaks**: Current and longest cooking streaks
- **Monthly Activity**: 6 months of activity data
- **Discovery Stats**: Unique ingredients tried, recipe diversity scores
- **Cuisine Preferences**: Based on actual saved recipes

## 🚀 How to Use

### Prerequisites

1. **MongoDB Running**: Ensure MongoDB is running on `localhost:27017`
2. **Python Dependencies**: Install required packages:
   ```bash
   pip install pymongo bcrypt numpy
   ```

### Option 1: Quick Generation (Recommended)

```bash
python run_data_generation.py
```

Select option 1 for quick generation with 75 users.

### Option 2: Custom Generation

```bash
python run_data_generation.py
```

Select option 2 to specify the number of users (1-200).

### Option 3: Direct Script

```bash
python generate_synthetic_data.py
```

Follow the prompts to customize generation.

## 📈 Expected Results

After generation, your database will contain:

- **75 Users** with diverse personas and realistic profiles
- **500-1000 Reviews** with authentic ratings and text
- **1000-2000 Search Entries** with realistic ingredient combinations
- **500-1500 Saved Recipes** distributed across users
- **300-800 Community Interactions** (review votes)
- **Complete Analytics Data** for all users

## 🔍 Verifying Generated Data

### Check User Count
```bash
mongosh "mongodb://localhost:27017/sisarasa" --eval "db.users.countDocuments({})"
```

### Check Reviews
```bash
mongosh "mongodb://localhost:27017/sisarasa" --eval "db.recipe_reviews.countDocuments({})"
```

### View Sample User
```javascript
// In MongoDB shell
db.users.findOne({}, {
  name: 1, 
  email: 1, 
  persona: 1,
  "analytics.total_reviews_given": 1,
  "analytics.total_recipe_saves": 1,
  "dashboard_data.search_stats.total_searches": 1
})
```

### Check Review Distribution
```javascript
// In MongoDB shell
db.recipe_reviews.aggregate([
  {$group: {_id: "$rating", count: {$sum: 1}}},
  {$sort: {_id: 1}}
])
```

## 🎯 Testing Recommendations

After generating data, test your recommendation system:

### 1. **KNN Recommendations**
- Search for ingredients like "chicken, rice, tomato"
- Verify diverse results based on ingredient matching

### 2. **Collaborative Filtering**
- Login as different users
- Check if recommendations vary based on user's review history

### 3. **Content-Based Filtering**
- Users with cuisine preferences should get relevant recommendations
- Dietary restrictions should be respected

### 4. **Popularity-Based**
- Highly-rated recipes should appear in trending sections

## 🛠️ Customization

### Adding More Personas
Edit `USER_PERSONAS` in `generate_synthetic_data.py` to add new user types.

### Adjusting Review Patterns
Modify `REVIEW_TEMPLATES` to change review text styles.

### Changing Ingredient Combinations
Update `INGREDIENT_COMBINATIONS` for different search patterns.

## 🔧 Troubleshooting

### MongoDB Connection Issues
- Ensure MongoDB is running: `mongod`
- Check connection string in script
- Verify database name matches your configuration

### Import Errors
```bash
pip install pymongo bcrypt numpy
```

### Memory Issues
- Reduce number of users if system runs out of memory
- Generate data in smaller batches

## 📝 Data Schema Compatibility

The generated data is fully compatible with your existing SisaRasa schema:

- **Users**: Matches `src/api/models/user.py` structure
- **Reviews**: Compatible with `src/api/models/community.py`
- **Recipes**: Follows your recipe data format
- **Analytics**: Integrates with existing analytics system

## 🎉 Next Steps

After generating synthetic data:

1. **Test Recommendations**: Try different ingredient searches
2. **Check Analytics**: View user dashboards and metrics
3. **Test Community Features**: Login as different users and interact
4. **Verify Hybrid Algorithm**: Ensure all recommendation types work
5. **Performance Testing**: Test system with realistic data volumes

Your SisaRasa system is now ready to showcase its full capabilities with realistic user data!
