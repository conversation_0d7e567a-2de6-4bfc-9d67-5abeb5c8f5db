<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Search Functionality</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-container { max-width: 600px; margin: 0 auto; }
        .ingredient-input { padding: 10px; border: 1px solid #ccc; border-radius: 5px; width: 100%; margin: 10px 0; }
        .search-btn { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .search-btn:disabled { background: #ccc; cursor: not-allowed; }
        .ingredient-tag { display: inline-block; background: #007bff; color: white; padding: 5px 10px; margin: 5px; border-radius: 15px; }
        .remove-tag { margin-left: 5px; cursor: pointer; font-weight: bold; }
        .results { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div id="app" class="test-container">
        <h1>Search Functionality Test</h1>
        
        <div>
            <h3>Add Ingredients:</h3>
            <input 
                v-model="currentInput" 
                @keydown.enter="addIngredient"
                class="ingredient-input" 
                placeholder="Type an ingredient and press Enter..."
            >
            
            <div v-if="selectedIngredients.length > 0">
                <h4>Selected Ingredients:</h4>
                <div v-for="(ingredient, index) in selectedIngredients" :key="index" class="ingredient-tag">
                    {{ ingredient }}
                    <span class="remove-tag" @click="removeIngredient(index)">&times;</span>
                </div>
            </div>
            
            <button 
                @click="testSearch" 
                :disabled="selectedIngredients.length === 0"
                class="search-btn"
            >
                Test Search ({{ selectedIngredients.length }} ingredients)
            </button>
        </div>
        
        <div class="results">
            <h3>Test Results:</h3>
            <div v-if="loading">Testing search functionality...</div>
            <div v-else-if="error" class="error">{{ error }}</div>
            <div v-else-if="success" class="success">{{ success }}</div>
            <div v-if="searchResults">
                <h4>API Response:</h4>
                <pre>{{ JSON.stringify(searchResults, null, 2) }}</pre>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    currentInput: '',
                    selectedIngredients: [],
                    loading: false,
                    error: null,
                    success: null,
                    searchResults: null
                }
            },
            methods: {
                addIngredient() {
                    const ingredient = this.currentInput.trim().toLowerCase();
                    if (ingredient && !this.selectedIngredients.includes(ingredient)) {
                        this.selectedIngredients.push(ingredient);
                        this.currentInput = '';
                    }
                },
                removeIngredient(index) {
                    this.selectedIngredients.splice(index, 1);
                },
                async testSearch() {
                    if (this.selectedIngredients.length === 0) return;
                    
                    this.loading = true;
                    this.error = null;
                    this.success = null;
                    this.searchResults = null;
                    
                    try {
                        const response = await fetch('http://127.0.0.1:5000/api/recommend', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                ingredients: this.selectedIngredients,
                                limit: 5,
                                min_score: 0.01,
                                strict: false
                            })
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        this.searchResults = data;
                        
                        if (data.status === 'ok' && data.recipes && data.recipes.length > 0) {
                            this.success = `✅ Search successful! Found ${data.recipes.length} recipes.`;
                        } else {
                            this.error = `❌ Search returned no results. Status: ${data.status}`;
                        }
                        
                    } catch (err) {
                        this.error = `❌ Search failed: ${err.message}`;
                        console.error('Search error:', err);
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }).mount('#app');
    </script>
</body>
</html>
