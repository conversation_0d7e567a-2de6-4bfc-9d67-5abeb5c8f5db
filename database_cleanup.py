#!/usr/bin/env python3
"""
Database Cleanup Script for SisaRasa Community Features

This script cleans up the database by:
1. Removing duplicate posts
2. Fixing invalid user references in shared recipes
3. Ensuring data consistency across collections
4. Removing any placeholder/dummy data
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from bson import ObjectId
import json

# Add the project root to the path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        # Try to get MongoDB URI from environment or use default
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def backup_collections(db, collections_to_backup):
    """Create backup of collections before cleanup."""
    print("\n📦 Creating backup of collections...")
    
    backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for collection_name in collections_to_backup:
        collection = db[collection_name]
        backup_collection_name = f"{collection_name}_backup_{backup_timestamp}"
        
        # Get all documents
        documents = list(collection.find())
        
        if documents:
            # Create backup collection
            backup_collection = db[backup_collection_name]
            backup_collection.insert_many(documents)
            print(f"  ✅ Backed up {len(documents)} documents from {collection_name} to {backup_collection_name}")
        else:
            print(f"  ⚠️  Collection {collection_name} is empty, skipping backup")

def remove_duplicate_posts(db):
    """Remove duplicate posts based on content."""
    print("\n🧹 Removing duplicate posts...")
    
    posts_collection = db['community_posts']
    
    # Find duplicates
    pipeline = [
        {
            "$group": {
                "_id": "$content",
                "posts": {"$push": "$$ROOT"},
                "count": {"$sum": 1}
            }
        },
        {
            "$match": {
                "count": {"$gt": 1}
            }
        }
    ]
    
    duplicates = list(posts_collection.aggregate(pipeline))
    
    removed_count = 0
    for duplicate_group in duplicates:
        posts = duplicate_group['posts']
        # Keep the oldest post, remove the rest
        posts_sorted = sorted(posts, key=lambda x: x.get('created_at', datetime.min))
        posts_to_remove = posts_sorted[1:]  # Remove all except the first (oldest)
        
        for post in posts_to_remove:
            post_id = post['_id']
            
            # Remove associated comments and likes
            db['post_comments'].delete_many({'post_id': str(post_id)})
            db['post_likes'].delete_many({'post_id': str(post_id)})
            
            # Remove the post
            posts_collection.delete_one({'_id': post_id})
            removed_count += 1
            
            print(f"  🗑️  Removed duplicate post: {post_id}")
    
    print(f"✅ Removed {removed_count} duplicate posts")
    return removed_count

def fix_shared_recipes(db):
    """Fix shared recipes with invalid user references."""
    print("\n🔧 Fixing shared recipes...")
    
    recipes_collection = db['recipes']
    users_collection = db['users']
    
    # Find user-submitted recipes
    user_recipes = list(recipes_collection.find({'original_id': {'$regex': '^user_'}}))
    
    fixed_count = 0
    removed_count = 0
    
    for recipe in user_recipes:
        recipe_id = recipe['_id']
        submitted_by = recipe.get('submitted_by')
        
        if not submitted_by:
            # Try to extract user ID from original_id
            original_id = recipe.get('original_id', '')
            if original_id.startswith('user_'):
                parts = original_id.split('_')
                if len(parts) >= 2:
                    potential_user_id = parts[1]
                    
                    # Check if this user exists
                    user_exists = users_collection.find_one({'_id': ObjectId(potential_user_id)})
                    if user_exists:
                        # Fix the recipe
                        recipes_collection.update_one(
                            {'_id': recipe_id},
                            {'$set': {'submitted_by': potential_user_id}}
                        )
                        fixed_count += 1
                        print(f"  🔧 Fixed recipe {recipe_id}: added submitted_by = {potential_user_id}")
                    else:
                        # Remove recipe with invalid user reference
                        recipes_collection.delete_one({'_id': recipe_id})
                        removed_count += 1
                        print(f"  🗑️  Removed recipe {recipe_id}: invalid user reference")
                else:
                    # Remove malformed recipe
                    recipes_collection.delete_one({'_id': recipe_id})
                    removed_count += 1
                    print(f"  🗑️  Removed recipe {recipe_id}: malformed original_id")
            else:
                # Remove recipe without proper user reference
                recipes_collection.delete_one({'_id': recipe_id})
                removed_count += 1
                print(f"  🗑️  Removed recipe {recipe_id}: no user reference")
        else:
            # Check if the user still exists
            try:
                user_id = ObjectId(submitted_by) if isinstance(submitted_by, str) else submitted_by
                user_exists = users_collection.find_one({'_id': user_id})
                if not user_exists:
                    # Remove recipe with non-existent user
                    recipes_collection.delete_one({'_id': recipe_id})
                    removed_count += 1
                    print(f"  🗑️  Removed recipe {recipe_id}: user {submitted_by} doesn't exist")
            except:
                # Remove recipe with invalid user ID format
                recipes_collection.delete_one({'_id': recipe_id})
                removed_count += 1
                print(f"  🗑️  Removed recipe {recipe_id}: invalid user ID format")
    
    print(f"✅ Fixed {fixed_count} recipes, removed {removed_count} invalid recipes")
    return fixed_count, removed_count

def clean_orphaned_data(db):
    """Remove orphaned comments and likes."""
    print("\n🧹 Cleaning orphaned data...")
    
    # Clean orphaned post comments
    post_comments = db['post_comments']
    posts = db['community_posts']
    
    orphaned_comments = 0
    for comment in post_comments.find():
        post_id = comment.get('post_id')
        if post_id:
            post_exists = posts.find_one({'_id': ObjectId(post_id) if isinstance(post_id, str) else post_id})
            if not post_exists:
                post_comments.delete_one({'_id': comment['_id']})
                orphaned_comments += 1
    
    # Clean orphaned post likes
    post_likes = db['post_likes']
    orphaned_likes = 0
    for like in post_likes.find():
        post_id = like.get('post_id')
        if post_id:
            post_exists = posts.find_one({'_id': ObjectId(post_id) if isinstance(post_id, str) else post_id})
            if not post_exists:
                post_likes.delete_one({'_id': like['_id']})
                orphaned_likes += 1
    
    # Clean orphaned comment likes
    comment_likes = db['comment_likes']
    orphaned_comment_likes = 0
    for like in comment_likes.find():
        comment_id = like.get('comment_id')
        if comment_id:
            comment_exists = post_comments.find_one({'_id': ObjectId(comment_id) if isinstance(comment_id, str) else comment_id})
            if not comment_exists:
                comment_likes.delete_one({'_id': like['_id']})
                orphaned_comment_likes += 1
    
    print(f"✅ Removed {orphaned_comments} orphaned comments, {orphaned_likes} orphaned post likes, {orphaned_comment_likes} orphaned comment likes")
    return orphaned_comments, orphaned_likes, orphaned_comment_likes

def update_post_counts(db):
    """Update like and comment counts for all posts."""
    print("\n📊 Updating post counts...")
    
    posts_collection = db['community_posts']
    comments_collection = db['post_comments']
    likes_collection = db['post_likes']
    
    updated_posts = 0
    for post in posts_collection.find():
        post_id = str(post['_id'])
        
        # Count comments
        comment_count = comments_collection.count_documents({'post_id': post_id})
        
        # Count likes
        like_count = likes_collection.count_documents({'post_id': post_id})
        
        # Update post
        posts_collection.update_one(
            {'_id': post['_id']},
            {
                '$set': {
                    'comment_count': comment_count,
                    'like_count': like_count
                }
            }
        )
        updated_posts += 1
    
    print(f"✅ Updated counts for {updated_posts} posts")
    return updated_posts

def main():
    """Main cleanup function."""
    print("🧹 SisaRasa Database Cleanup")
    print("=" * 50)
    print(f"Cleanup started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    try:
        # Create backups
        collections_to_backup = ['community_posts', 'post_comments', 'post_likes', 'recipes']
        backup_collections(db, collections_to_backup)
        
        # Perform cleanup operations
        duplicate_posts_removed = remove_duplicate_posts(db)
        recipes_fixed, recipes_removed = fix_shared_recipes(db)
        orphaned_comments, orphaned_likes, orphaned_comment_likes = clean_orphaned_data(db)
        updated_posts = update_post_counts(db)
        
        # Summary
        print("\n📋 CLEANUP SUMMARY")
        print("=" * 50)
        print(f"Duplicate posts removed: {duplicate_posts_removed}")
        print(f"Recipes fixed: {recipes_fixed}")
        print(f"Invalid recipes removed: {recipes_removed}")
        print(f"Orphaned comments removed: {orphaned_comments}")
        print(f"Orphaned likes removed: {orphaned_likes}")
        print(f"Orphaned comment likes removed: {orphaned_comment_likes}")
        print(f"Posts with updated counts: {updated_posts}")
        
        print(f"\n✅ Cleanup completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    # Ask for confirmation before proceeding
    print("⚠️  This script will modify your database. Make sure you have a backup!")
    response = input("Do you want to proceed? (yes/no): ").lower().strip()
    
    if response in ['yes', 'y']:
        main()
    else:
        print("Cleanup cancelled.")
