
from pymongo import MongoClient

# Your Atlas connection string
atlas_uri = "mongodb+srv://farahfiqh:<EMAIL>/?retryWrites=true&w=majority&appName=SisaRasa"
try:
    client = MongoClient(atlas_uri)
    # Test connection
    client.admin.command('ping')
    print("✅ Successfully connected to Atlas!")
    
    # List databases
    databases = client.list_database_names()
    print(f"Available databases: {databases}")
    
except Exception as e:
    print(f"❌ Connection failed: {e}")
finally:
    client.close()