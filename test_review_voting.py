#!/usr/bin/env python3
"""
Test script for review voting functionality in SisaRasa crowd sourcing features.
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def create_and_login_user(suffix):
    """Create a user and login to get token."""
    user_data = {
        "name": f"Test User {suffix}",
        "email": f"testuser{suffix}_{int(time.time())}@example.com",
        "password": "testpass123"
    }

    # Create user
    response = requests.post(f"{BASE_URL}/api/auth/signup", json=user_data)
    if response.status_code != 201:
        print(f"❌ Failed to create user {suffix}")
        return None

    # Login user
    login_data = {"email": user_data['email'], "password": user_data['password']}
    response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
    if response.status_code == 200:
        data = response.json()
        token = data.get('token')
        print(f"✅ User {suffix} created and logged in")
        return token, user_data['name']

    print(f"❌ Failed to login user {suffix}")
    return None

def get_sample_recipe():
    """Get a sample recipe for testing."""
    response = requests.post(f"{BASE_URL}/api/recommend",
                           json={"ingredients": ["beef", "potato"], "limit": 1})
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'ok' and data.get('recipes'):
            recipe = data['recipes'][0]
            return recipe['id'], recipe['name']
    return None, None

def add_review(token, recipe_id, rating, review_text):
    """Add a review to a recipe."""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    review_data = {"rating": rating, "review_text": review_text}

    response = requests.post(f"{BASE_URL}/api/recipe/{recipe_id}/review",
                           json=review_data, headers=headers)
    return response.status_code == 200

def get_reviews(recipe_id):
    """Get reviews for a recipe."""
    response = requests.get(f"{BASE_URL}/api/recipe/{recipe_id}/reviews")
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            return data.get('reviews', [])
    return []

def vote_on_review(token, review_id, vote_type):
    """Vote on a review."""
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    vote_data = {"vote_type": vote_type}

    response = requests.post(f"{BASE_URL}/api/review/{review_id}/vote",
                           json=vote_data, headers=headers)
    return response.status_code == 200, response.text

def test_review_voting():
    """Test the complete review voting functionality."""
    print("🗳️  Testing Review Voting Functionality")
    print("=" * 50)

    # Create two users
    print("\n👥 Creating test users...")
    user1_data = create_and_login_user("A")
    user2_data = create_and_login_user("B")

    if not user1_data or not user2_data:
        print("❌ Failed to create test users")
        return

    token1, name1 = user1_data
    token2, name2 = user2_data

    # Get a sample recipe
    print("\n🍽️  Getting sample recipe...")
    recipe_id, recipe_name = get_sample_recipe()
    if not recipe_id:
        print("❌ Failed to get sample recipe")
        return

    print(f"✅ Using recipe: {recipe_name} (ID: {recipe_id})")

    # User 1 adds a review
    print(f"\n📝 {name1} adding a review...")
    review_text = "This recipe is absolutely fantastic! The flavors blend perfectly and it's easy to make."
    if add_review(token1, recipe_id, 5, review_text):
        print("✅ Review added successfully")
    else:
        print("❌ Failed to add review")
        return

    # Get the review ID
    print("\n🔍 Getting review details...")
    reviews = get_reviews(recipe_id)
    if not reviews:
        print("❌ No reviews found")
        return

    review = reviews[0]  # Get the first review
    review_id = review.get('_id') or review.get('id')
    print(f"✅ Found review by {review['user_name']}: \"{review['review_text'][:50]}...\"")
    print(f"   Review ID: {review_id}")
    print(f"   Full review data: {review}")  # Debug info

    # User 2 votes "helpful" on the review
    print(f"\n👍 {name2} voting 'helpful' on the review...")
    success, response_text = vote_on_review(token2, review_id, "helpful")
    if success:
        print("✅ 'Helpful' vote added successfully")
    else:
        print(f"❌ Failed to vote helpful: {response_text}")

    # Check updated vote counts
    print("\n📊 Checking updated vote counts...")
    reviews = get_reviews(recipe_id)
    if reviews:
        review = reviews[0]
        helpful_votes = review.get('helpful_votes', 0)
        unhelpful_votes = review.get('unhelpful_votes', 0)
        print(f"✅ Vote counts updated:")
        print(f"   👍 Helpful votes: {helpful_votes}")
        print(f"   👎 Unhelpful votes: {unhelpful_votes}")

    # User 2 changes vote to "unhelpful"
    print(f"\n👎 {name2} changing vote to 'unhelpful'...")
    success, response_text = vote_on_review(token2, review_id, "unhelpful")
    if success:
        print("✅ Vote changed to 'unhelpful' successfully")
    else:
        print(f"❌ Failed to change vote: {response_text}")

    # Check final vote counts
    print("\n📊 Checking final vote counts...")
    reviews = get_reviews(recipe_id)
    if reviews:
        review = reviews[0]
        helpful_votes = review.get('helpful_votes', 0)
        unhelpful_votes = review.get('unhelpful_votes', 0)
        print(f"✅ Final vote counts:")
        print(f"   👍 Helpful votes: {helpful_votes}")
        print(f"   👎 Unhelpful votes: {unhelpful_votes}")

    # Test voting on own review (should fail)
    print(f"\n🚫 Testing {name1} voting on their own review (should fail)...")
    success, response_text = vote_on_review(token1, review_id, "helpful")
    if not success:
        print("✅ Correctly prevented user from voting on their own review")
    else:
        print("❌ User was allowed to vote on their own review (this should not happen)")

    print("\n🎉 Review voting functionality test completed!")
    print("\n✅ Features tested:")
    print("   👍 Helpful voting")
    print("   👎 Unhelpful voting")
    print("   🔄 Vote changing")
    print("   📊 Vote count tracking")
    print("   🚫 Self-voting prevention")

if __name__ == "__main__":
    test_review_voting()
