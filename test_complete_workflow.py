#!/usr/bin/env python3
"""
Complete test workflow: Submit recipe, verify it appears with correct metadata and delete button.
"""

import requests
import json
import sys
import time

BASE_URL = "http://localhost:5000"

def create_test_user_and_login():
    """Create a test user and login to get a token."""
    print("Creating test user and logging in...")
    
    timestamp = str(int(time.time()))
    test_user = {
        "name": "Complete Test User",
        "email": f"complete{timestamp}@example.com",
        "password": "testpassword123"
    }
    
    try:
        # Register the user
        register_response = requests.post(f"{BASE_URL}/api/auth/signup", json=test_user)
        print(f"Register response: {register_response.status_code}")
        
        # Login
        login_data = {
            "email": test_user["email"],
            "password": test_user["password"]
        }
        
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        print(f"Login response: {login_response.status_code}")
        
        if login_response.status_code == 200:
            result = login_response.json()
            token = result.get('token')
            user_info = result.get('user', {})
            print(f"✓ Successfully logged in as {user_info.get('name')} (ID: {user_info.get('id')})")
            return token, user_info.get('id'), user_info.get('name')
        else:
            print(f"✗ Login failed: {login_response.text}")
            return None, None, None
            
    except Exception as e:
        print(f"✗ Error during authentication: {e}")
        return None, None, None

def submit_test_recipe(token, user_name):
    """Submit a test recipe using form data."""
    print(f"\n=== Submitting Test Recipe ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # Prepare form data
    form_data = {
        'name': f'Test Recipe by {user_name}',
        'description': 'A test recipe to verify metadata and delete functionality',
        'cuisine': 'Test Cuisine',
        'ingredients': json.dumps(['1 cup test ingredient', '2 tbsp test spice']),
        'instructions': json.dumps(['Mix ingredients', 'Cook for 5 minutes', 'Serve hot']),
        'prep_time': '15',
        'cook_time': '20',
        'servings': '3',
        'difficulty': 'Easy'
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/recipe/submit", data=form_data, headers=headers)
        print(f"Submit recipe response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Recipe submitted successfully")
            print(f"Recipe ID: {result.get('recipe_id')}")
            return result.get('recipe_id')
        else:
            print(f"✗ Recipe submission failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error submitting recipe: {e}")
        return None

def verify_recipe_in_community(token, current_user_id, user_name, recipe_id):
    """Verify the submitted recipe appears correctly in the community page."""
    print(f"\n=== Verifying Recipe in Community ===")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Wait a moment for the recipe to be processed
        time.sleep(2)
        
        response = requests.get(f"{BASE_URL}/api/shared-recipes", headers=headers)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Number of recipes returned: {len(data)}")
            
            # Look for our submitted recipe
            our_recipe = None
            for recipe in data:
                if recipe.get('id') == recipe_id or recipe.get('name', '').startswith('Test Recipe by'):
                    our_recipe = recipe
                    break
            
            if our_recipe:
                print(f"\n✓ Found our submitted recipe!")
                print(f"Recipe Name: {our_recipe.get('name')}")
                print(f"Recipe User ID: {our_recipe.get('user_id')}")
                print(f"Recipe Username: {our_recipe.get('username')}")
                print(f"Recipe Prep Time: {our_recipe.get('prep_time')}")
                print(f"Recipe Cook Time: {our_recipe.get('cook_time')}")
                print(f"Recipe Servings: {our_recipe.get('servings')}")
                print(f"Recipe Difficulty: {our_recipe.get('difficulty')}")
                
                # Check ownership for delete button
                is_owned = our_recipe.get('user_id') == current_user_id
                print(f"\n=== Delete Button Logic ===")
                print(f"Current User ID: {current_user_id}")
                print(f"Recipe User ID: {our_recipe.get('user_id')}")
                print(f"Is Owned (delete button should show): {is_owned}")
                
                # Check metadata
                metadata_correct = (
                    our_recipe.get('prep_time') == 15 and
                    our_recipe.get('cook_time') == 20 and
                    our_recipe.get('servings') == 3 and
                    our_recipe.get('difficulty') == 'Easy'
                )
                print(f"Metadata correct: {metadata_correct}")
                
                # Check username
                username_correct = our_recipe.get('username') == user_name
                print(f"Username correct: {username_correct}")
                
                return {
                    'found': True,
                    'owned': is_owned,
                    'metadata_correct': metadata_correct,
                    'username_correct': username_correct
                }
            else:
                print(f"✗ Could not find our submitted recipe in the community")
                print("Available recipes:")
                for recipe in data:
                    print(f"  - {recipe.get('name')} by {recipe.get('username')}")
                return {
                    'found': False,
                    'owned': False,
                    'metadata_correct': False,
                    'username_correct': False
                }
        else:
            print(f"✗ API call failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Error calling API: {e}")
        return None

def main():
    """Run complete workflow test."""
    print("=== Complete Community Recipe Workflow Test ===\n")
    
    # Step 1: Authentication
    token, user_id, user_name = create_test_user_and_login()
    if not token:
        print("✗ Could not authenticate, stopping test")
        return 1
    
    # Step 2: Submit a recipe
    recipe_id = submit_test_recipe(token, user_name)
    if not recipe_id:
        print("✗ Could not submit recipe, stopping test")
        return 1
    
    # Step 3: Verify recipe appears correctly
    verification = verify_recipe_in_community(token, user_id, user_name, recipe_id)
    if not verification:
        print("✗ Could not verify recipe, test failed")
        return 1
    
    # Step 4: Summary
    print(f"\n=== Test Results Summary ===")
    print(f"Authentication: ✓")
    print(f"Recipe submission: ✓")
    print(f"Recipe found in community: {'✓' if verification['found'] else '✗'}")
    print(f"Recipe ownership (delete button): {'✓' if verification['owned'] else '✗'}")
    print(f"Metadata display: {'✓' if verification['metadata_correct'] else '✗'}")
    print(f"Username display: {'✓' if verification['username_correct'] else '✗'}")
    
    all_passed = all([
        verification['found'],
        verification['owned'],
        verification['metadata_correct'],
        verification['username_correct']
    ])
    
    if all_passed:
        print("\n🎉 All tests passed! The community recipe fixes are working correctly.")
        print("✓ Delete buttons will appear for recipe owners")
        print("✓ Recipe metadata is displayed correctly")
        print("✓ Usernames are displayed instead of 'Anonymous User'")
    else:
        print("\n❌ Some tests failed. Check the issues above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
