<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Profile | Sisa Rasa</title>

  <!-- Styles and Fonts -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@500;600&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">

  <!-- Vue -->
  <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>

  <!-- SweetAlert for notifications -->
  <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

  <style>
    body {
      font-family: 'Poppins', sans-serif;
      background: url("{{ url_for('static', filename='images/bg.png') }}") no-repeat center center fixed;
      background-size: cover;
      margin: 0;
    }
    /* Sidebar styles */
    .sidebar {
      background-color: #083640;
      min-height: 100vh;
      padding-top: 1rem;
      color: white;
      border-right: 1px solid rgba(255,255,255,0.1);
    }
    /* Logo styles */
    .logo-container {
      background-color: #072a32;
      padding: 1.5rem 1rem;
      margin-bottom: 2rem;
      text-align: center;
      border-radius: 0 0 10px 10px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    .logo-container img {
      width: 80px;
      height: auto;
      margin-bottom: 0.75rem;
      filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
      transition: transform 0.3s ease;
    }
    .logo-container:hover img {
      transform: scale(1.05);
    }
    .logo-container h5 {
      font-size: 1.75rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
      color: white;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    .logo-container small {
      font-size: 0.8rem;
      opacity: 0.9;
      display: block;
      color: #fedf2f;
    }
    .nav-links {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
      padding-left: 0;
      margin-top: 2rem;
    }
    .nav-links a {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      text-decoration: none;
      color: white;
      border-radius: 0;
      transition: background 0.3s;
      border-left: 4px solid transparent;
    }
    .nav-links a.active {
      background-color: #ea5e18;
      border-left: 4px solid #fedf2f;
    }
    .nav-links a:hover:not(.active) {
      background-color: rgba(234, 94, 24, 0.3);
    }

    /* Header styles */
    .header-bar {
      background-color: transparent;
      padding: 1rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #083640;
    }
    .header-bar h5 {
      font-weight: 700;
      margin-bottom: 0;
    }
    .user-profile {
      background-color: #083640;
      border-radius: 2rem;
      padding: 0.5rem 1rem;
      display: flex;
      align-items: center;
      color: white;
    }
    .user-profile img {
      border: 2px solid white;
    }

    /* Profile card styles */
    .profile-card {
      background-color: #ffffff;
      border-radius: 1rem;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      padding: 2rem;
      transition: all 0.3s ease;
      margin-bottom: 1.5rem;
    }
    .profile-card:hover {
      box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    }

    /* Profile sections */
    .profile-section {
      margin-bottom: 2.5rem;
      position: relative;
    }
    .profile-section:not(:last-child)::after {
      content: '';
      position: absolute;
      bottom: -1.25rem;
      left: 0;
      right: 0;
      height: 1px;
      background-color: rgba(0,0,0,0.1);
    }
    .profile-section h5 {
      margin-bottom: 1.5rem;
      color: #083640;
      font-weight: 600;
      display: flex;
      align-items: center;
    }
    .profile-section h5 i {
      color: #ea5e18;
      margin-right: 0.5rem;
      font-size: 1.25rem;
    }

    /* Profile image */
    .profile-image-container {
      text-align: center;
      margin-bottom: 2rem;
    }
    .profile-avatar {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background-color: #ea5e18;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2.5rem;
      font-weight: 600;
      margin: 0 auto 1.5rem;
    }
    .profile-image {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #ea5e18;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Form elements */
    .form-label {
      font-weight: 600;
      color: #083640;
    }
    .form-control {
      border-radius: 0.5rem;
      padding: 0.75rem 1rem;
      border: 1px solid #ddd;
      transition: all 0.3s;
    }
    .form-control:focus {
      border-color: #ea5e18;
      box-shadow: 0 0 0 0.25rem rgba(234, 94, 24, 0.25);
    }
    .btn-primary {
      background-color: #ea5e18;
      border-color: #ea5e18;
      border-radius: 0.5rem;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      transition: all 0.3s;
    }
    .btn-primary:hover, .btn-primary:focus {
      background-color: #d15415;
      border-color: #d15415;
      transform: translateY(-2px);
    }
    .btn-outline-primary {
      color: #ea5e18;
      border-color: #ea5e18;
      border-radius: 0.5rem;
      transition: all 0.3s;
    }
    .btn-outline-primary:hover {
      background-color: #ea5e18;
      color: white;
    }

    /* Animation styles */
    .fade-in {
      animation: fadeIn 0.8s ease forwards;
      opacity: 0;
      transform: translateY(20px);
    }
    @keyframes fadeIn {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    /* Loading spinner */
    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    /* Footer styles */
    .footer {
      background-color: #072a32;
      color: rgba(255,255,255,0.7);
      text-align: center;
      padding: 1rem;
      margin-top: 2rem;
      font-size: 0.8rem;
      border-radius: 0.5rem;
    }
    .footer a {
      color: #fedf2f;
      text-decoration: none;
      transition: color 0.3s;
    }
    .footer a:hover {
      color: #ea5e18;
    }
  </style>
</head>

<body>
  <div id="app" class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <div class="col-md-2 sidebar">
        <div class="logo-container">
          <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Sisa Rasa Logo">
          <h5>Sisa Rasa</h5>
          <small>Rasa Baru</small>
          <small>Sisa Lama</small>
        </div>
        <nav class="nav-links">
          <a href="{{ url_for('main.dashboard') }}"><i class='bx bx-grid-alt'></i>Dashboard</a>
          <a href="{{ url_for('main.save_recipe_page') }}"><i class='bx bx-book-heart'></i>Save Recipe</a>
          <a href="{{ url_for('main.shared_recipe_page') }}"><i class='bx bx-share-alt'></i>Share Recipe</a>
          <a href="{{ url_for('main.community_page') }}"><i class='bx bx-group'></i>Community</a>
          <a href="{{ url_for('main.profile_page') }}" class="active"><i class='bx bx-user'></i>Profile</a>
          <a href="{{ url_for('main.home') }}" id="logoutBtn"><i class='bx bx-log-out'></i>Log Out</a>
        </nav>
      </div>

      <!-- Main Content -->
      <div class="col-md-10">
        <!-- Header -->
        <div class="header-bar">
          <div>
            <h5>Your Profile</h5>
            <p>Manage your account information 👤</p>
          </div>
          <div class="user-profile">
            <span class="me-2">${ userName }</span>
            <img v-if="profileImageUrl" :src="profileImageUrl" class="rounded-circle" alt="User" width="40" height="40" style="object-fit: cover;">
            <img v-else src="{{ url_for('static', filename='images/user.png') }}" class="rounded-circle" alt="User" width="40">
          </div>
        </div>

        <div class="container mt-4">
          <!-- Loading State -->
          <div v-if="loading" class="loading-spinner">
            <div class="spinner-border text-warning" role="status">
              <span class="visually-hidden">Loading...</span>
            </div>
          </div>

          <!-- Profile Content -->
          <div v-else class="row">
            <div class="col-md-8 mx-auto">
              <div class="profile-card fade-in">
                <!-- Profile Image Section -->
                <div class="profile-image-container">
                  <div v-if="profileImageUrl" class="text-center">
                    <img :src="profileImageUrl" class="profile-image" alt="Profile Image">
                  </div>
                  <div v-else class="profile-avatar">${ userInitials }</div>

                  <div class="text-center mt-3">
                    <label for="profile-image-upload" class="btn btn-sm btn-outline-primary">
                      <i class='bx bx-upload me-1'></i> Upload Photo
                    </label>
                    <input type="file" id="profile-image-upload" class="d-none" @change="uploadProfileImage" accept="image/*">
                  </div>
                </div>
                <h4 class="text-center mb-2">${ userName }</h4>
                <p class="text-center text-muted mb-4">${ userEmail }</p>

                <!-- Profile Information Section -->
                <div class="profile-section">
                  <h5><i class='bx bx-user-circle'></i>Profile Information</h5>
                  <form @submit.prevent="updateProfile">
                    <div class="mb-3">
                      <label for="name" class="form-label">Name</label>
                      <input type="text" class="form-control" id="name" v-model="formData.name" required>
                    </div>
                    <div class="mb-3">
                      <label for="email" class="form-label">Email</label>
                      <input type="email" class="form-control" id="email" v-model="userEmail" disabled>
                      <small class="text-muted">Email cannot be changed</small>
                    </div>
                    <div class="d-grid">
                      <button type="submit" class="btn btn-primary" :disabled="updateProfileLoading">
                        <span v-if="updateProfileLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Update Profile
                      </button>
                    </div>
                  </form>
                </div>

                <!-- Change Password Section -->
                <div class="profile-section">
                  <h5><i class='bx bx-lock-alt'></i>Change Password</h5>
                  <form @submit.prevent="changePassword">
                    <div class="mb-3">
                      <label for="currentPassword" class="form-label">Current Password</label>
                      <input type="password" class="form-control" id="currentPassword" v-model="passwordData.currentPassword" required>
                    </div>
                    <div class="mb-3">
                      <label for="newPassword" class="form-label">New Password</label>
                      <input type="password" class="form-control" id="newPassword" v-model="passwordData.newPassword" required minlength="6">
                      <small class="text-muted">Password must be at least 6 characters long</small>
                    </div>
                    <div class="mb-3">
                      <label for="confirmPassword" class="form-label">Confirm New Password</label>
                      <input type="password" class="form-control" id="confirmPassword" v-model="passwordData.confirmPassword" required>
                    </div>
                    <div class="d-grid">
                      <button type="submit" class="btn btn-primary" :disabled="changePasswordLoading">
                        <span v-if="changePasswordLoading" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Change Password
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer mt-5">
          <p>&copy; 2025 Sisa Rasa. All rights reserved. <a href="#">Terms of Service</a> | <a href="#">Privacy Policy</a></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Vue App -->
  <script>
    const { createApp } = Vue;

    // Check if token exists
    const token = localStorage.getItem('token');
    if (!token) {
      window.location.href = '/login';
    }

    createApp({
      // Change delimiters to avoid conflict with Jinja2
      delimiters: ['${', '}'],
      data() {
        return {
          userName: "",
          userEmail: "",
          userInitials: "",
          profileImageUrl: null,
          loading: true,
          updateProfileLoading: false,
          changePasswordLoading: false,
          uploadImageLoading: false,
          formData: {
            name: ""
          },
          passwordData: {
            currentPassword: "",
            newPassword: "",
            confirmPassword: ""
          }
        }
      },
      mounted() {
        this.fetchUserInfo();
        this.fetchProfileImage();
      },
      methods: {
        logout() {
          // Clear local storage
          localStorage.removeItem('token');
          localStorage.removeItem('userId');
          localStorage.removeItem('userName');

          // Redirect to home page
          window.location.href = '/';
        },
        fetchUserInfo() {
          this.loading = true;

          fetch('/api/auth/me', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          .then(res => {
            if (!res.ok) {
              throw new Error('Failed to get user info');
            }
            return res.json();
          })
          .then(data => {
            this.loading = false;
            if (data.status === 'success' && data.user) {
              this.userName = data.user.name;
              this.userEmail = data.user.email;
              this.formData.name = data.user.name;

              // Generate initials from name
              if (this.userName) {
                const parts = this.userName.split(' ');
                this.userInitials = parts.map(p => p[0]).join('').toUpperCase();
              } else {
                this.userInitials = "SR";
              }
            }
          })
          .catch(err => {
            this.loading = false;
            console.error('Error fetching user info:', err);
            // Redirect to login if authentication fails
            if (err.message.includes('authentication')) {
              window.location.href = '/login';
            }
          });
        },
        updateProfile() {
          if (!this.formData.name) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Name is required',
              confirmButtonColor: '#ea5e18'
            });
            return;
          }

          this.updateProfileLoading = true;

          fetch('/api/auth/update-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              name: this.formData.name
            })
          })
          .then(res => {
            if (!res.ok) {
              throw new Error('Failed to update profile');
            }
            return res.json();
          })
          .then(data => {
            this.updateProfileLoading = false;
            if (data.status === 'success') {
              this.userName = data.user.name;

              // Update initials
              const parts = this.userName.split(' ');
              this.userInitials = parts.map(p => p[0]).join('').toUpperCase();

              // Update localStorage
              localStorage.setItem('userName', this.userName);

              Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Profile updated successfully',
                timer: 2000,
                showConfirmButton: false
              });
            } else {
              throw new Error(data.message || 'Failed to update profile');
            }
          })
          .catch(err => {
            this.updateProfileLoading = false;
            console.error('Error updating profile:', err);

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: err.message || 'Failed to update profile. Please try again.',
              confirmButtonColor: '#ea5e18'
            });
          });
        },
        changePassword() {
          // Validate passwords
          if (this.passwordData.newPassword !== this.passwordData.confirmPassword) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'New passwords do not match',
              confirmButtonColor: '#ea5e18'
            });
            return;
          }

          if (this.passwordData.newPassword.length < 6) {
            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: 'Password must be at least 6 characters long',
              confirmButtonColor: '#ea5e18'
            });
            return;
          }

          this.changePasswordLoading = true;

          fetch('/api/auth/change-password', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
              currentPassword: this.passwordData.currentPassword,
              newPassword: this.passwordData.newPassword
            })
          })
          .then(res => {
            if (!res.ok) {
              return res.json().then(data => {
                throw new Error(data.message || 'Failed to change password');
              });
            }
            return res.json();
          })
          .then(data => {
            this.changePasswordLoading = false;
            if (data.status === 'success') {
              // Reset form
              this.passwordData = {
                currentPassword: "",
                newPassword: "",
                confirmPassword: ""
              };

              Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Password changed successfully',
                timer: 2000,
                showConfirmButton: false
              });
            } else {
              throw new Error(data.message || 'Failed to change password');
            }
          })
          .catch(err => {
            this.changePasswordLoading = false;
            console.error('Error changing password:', err);

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: err.message || 'Failed to change password. Please try again.',
              confirmButtonColor: '#ea5e18'
            });
          });
        },
        fetchProfileImage() {
          // Check if user has a profile image
          fetch('/api/auth/profile-image/current', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          })
          .then(res => {
            if (!res.ok) {
              if (res.status !== 404) {
                // If error other than 404, log it
                console.error('Error fetching profile image:', res.statusText);
              }
              // If 404, we'll just use the initials avatar
              return null;
            }
            return res.json();
          })
          .then(data => {
            if (data && data.status === 'success' && data.profile_image) {
              // If successful, set the profile image URL (which is now a data URI)
              this.profileImageUrl = data.profile_image;
            }
          })
          .catch(err => {
            console.error('Error fetching profile image:', err);
          });
        },

        uploadProfileImage(event) {
          const file = event.target.files[0];
          if (!file) return;

          // Validate file type
          const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
          if (!allowedTypes.includes(file.type)) {
            Swal.fire({
              icon: 'error',
              title: 'Invalid File Type',
              text: 'Please upload a valid image file (JPEG, PNG, or GIF)',
              confirmButtonColor: '#ea5e18'
            });
            return;
          }

          // Validate file size (max 5MB)
          const maxSize = 5 * 1024 * 1024; // 5MB
          if (file.size > maxSize) {
            Swal.fire({
              icon: 'error',
              title: 'File Too Large',
              text: 'Please upload an image smaller than 5MB',
              confirmButtonColor: '#ea5e18'
            });
            return;
          }

          // Create form data
          const formData = new FormData();
          formData.append('image', file);

          // Show loading state
          this.uploadImageLoading = true;

          // Upload the image
          fetch('/api/auth/upload-profile-image', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`
            },
            body: formData
          })
          .then(res => {
            if (!res.ok) {
              throw new Error('Failed to upload profile image');
            }
            return res.json();
          })
          .then(data => {
            this.uploadImageLoading = false;
            if (data.status === 'success') {
              // Update the profile image URL with the data URI
              this.profileImageUrl = data.profile_image;

              Swal.fire({
                icon: 'success',
                title: 'Success',
                text: 'Profile image uploaded successfully',
                timer: 2000,
                showConfirmButton: false
              });
            } else {
              throw new Error(data.message || 'Failed to upload profile image');
            }
          })
          .catch(err => {
            this.uploadImageLoading = false;
            console.error('Error uploading profile image:', err);

            Swal.fire({
              icon: 'error',
              title: 'Error',
              text: err.message || 'Failed to upload profile image. Please try again.',
              confirmButtonColor: '#ea5e18'
            });
          });

          // Reset the file input
          event.target.value = '';
        },

        logout() {
          // Clear local storage
          localStorage.removeItem('token');
          localStorage.removeItem('userId');
          localStorage.removeItem('userName');

          // Redirect to login page
          window.location.href = '/login';
        }
      }
    }).mount('#app');
  </script>

  <!-- Boxicons -->
  <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
</body>
</html>
