#!/usr/bin/env python3
"""
Recommendation Engine Data Optimizer

This comprehensive script optimizes data for the SisaRasa recommendation engine by:
1. Generating culturally authentic Malaysian reviews
2. Implementing realistic rating distributions
3. Creating user preference patterns
4. Solving cold start problems
5. Ensuring recommendation algorithm performance
"""

import os
import sys
import json
import random
import math
from datetime import datetime, timed<PERSON>ta
from pymongo import MongoClient
from bson import ObjectId
from malaysian_review_generator import (
    get_recipe_context, select_persona_for_recipe, 
    generate_review_content, MALAYSIAN_PERSONAS
)

# Rating distribution strategy (realistic patterns)
RATING_DISTRIBUTION = {
    'excellent_recipes': {  # Top 20% of recipes
        'weights': [0.02, 0.08, 0.20, 0.35, 0.35],  # 1,2,3,4,5 stars
        'avg_rating_range': (4.2, 4.8)
    },
    'good_recipes': {  # Next 50% of recipes
        'weights': [0.05, 0.10, 0.25, 0.40, 0.20],  # 1,2,3,4,5 stars
        'avg_rating_range': (3.5, 4.2)
    },
    'average_recipes': {  # Next 25% of recipes
        'weights': [0.10, 0.15, 0.40, 0.25, 0.10],  # 1,2,3,4,5 stars
        'avg_rating_range': (2.8, 3.5)
    },
    'poor_recipes': {  # Bottom 5% of recipes
        'weights': [0.25, 0.30, 0.30, 0.10, 0.05],  # 1,2,3,4,5 stars
        'avg_rating_range': (1.5, 2.8)
    }
}

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        client = MongoClient(mongo_uri)
        db = client['sisarasa']
        
        # Test connection
        db.command('ping')
        print(f"✅ Connected to MongoDB: {mongo_uri}")
        return db, client
    except Exception as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return None, None

def load_analysis_data():
    """Load the gap analysis data."""
    try:
        with open('recipe_review_gap_analysis.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ Please run analyze_recipe_review_gaps.py first")
        return None

def backup_collections(db):
    """Create backups before optimization."""
    print("\n📦 Creating backups...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # Backup recipe_reviews
    reviews_collection = db['recipe_reviews']
    backup_collection = db[f'recipe_reviews_backup_optimization_{timestamp}']
    
    reviews = list(reviews_collection.find())
    if reviews:
        backup_collection.insert_many(reviews)
        print(f"  📦 Backed up {len(reviews)} reviews")
    
    print("✅ Backup completed")

def categorize_recipes_by_quality(db, recipes_data):
    """Categorize recipes by expected quality for realistic rating distribution."""
    print("\n📊 Categorizing recipes by quality...")
    
    recipes_collection = db['recipes']
    
    # Get all recipes with additional context
    all_recipes = []
    for recipe_info in recipes_data:
        recipe_id = recipe_info['recipe_id']
        recipe = recipes_collection.find_one({'_id': ObjectId(recipe_id)})
        if recipe:
            recipe_context = get_recipe_context(recipe)
            
            # Calculate quality score based on various factors
            quality_score = calculate_recipe_quality_score(recipe, recipe_context)
            
            all_recipes.append({
                'recipe_id': recipe_id,
                'recipe': recipe,
                'context': recipe_context,
                'quality_score': quality_score,
                'target_reviews': recipe_info['target_reviews']
            })
    
    # Sort by quality score and categorize
    all_recipes.sort(key=lambda x: x['quality_score'], reverse=True)
    
    total_recipes = len(all_recipes)
    categories = {
        'excellent_recipes': all_recipes[:int(total_recipes * 0.20)],  # Top 20%
        'good_recipes': all_recipes[int(total_recipes * 0.20):int(total_recipes * 0.70)],  # Next 50%
        'average_recipes': all_recipes[int(total_recipes * 0.70):int(total_recipes * 0.95)],  # Next 25%
        'poor_recipes': all_recipes[int(total_recipes * 0.95):]  # Bottom 5%
    }
    
    print(f"  🌟 Excellent recipes: {len(categories['excellent_recipes'])}")
    print(f"  👍 Good recipes: {len(categories['good_recipes'])}")
    print(f"  😐 Average recipes: {len(categories['average_recipes'])}")
    print(f"  👎 Poor recipes: {len(categories['poor_recipes'])}")
    
    return categories

def calculate_recipe_quality_score(recipe, recipe_context):
    """Calculate a quality score for realistic rating distribution."""
    score = 50  # Base score
    
    # User-submitted recipes get bonus (they're usually tested)
    if recipe.get('is_user_submitted', False):
        score += 20
    
    # Traditional cuisines get bonus (familiar to Malaysian users)
    if recipe_context['is_traditional']:
        score += 15
    
    # Recipe name quality (longer, more descriptive names often indicate better recipes)
    name_length = len(recipe_context['name'])
    if name_length > 20:
        score += 10
    elif name_length < 10:
        score -= 5
    
    # Difficulty factor (medium difficulty often preferred)
    difficulty = recipe_context['difficulty'].lower()
    if difficulty == 'medium':
        score += 10
    elif difficulty == 'hard':
        score -= 5
    
    # Add some randomness for natural distribution
    score += random.randint(-15, 15)
    
    return max(0, min(100, score))  # Clamp between 0-100

def generate_realistic_ratings(category_name, num_reviews):
    """Generate realistic rating distribution for a recipe category."""
    distribution = RATING_DISTRIBUTION[category_name]
    weights = distribution['weights']
    
    ratings = []
    for _ in range(num_reviews):
        # Use weighted random selection
        rating = random.choices([1, 2, 3, 4, 5], weights=weights)[0]
        ratings.append(rating)
    
    return ratings

def create_user_preference_profiles(db):
    """Create diverse user preference profiles for better recommendations."""
    print("\n👥 Creating user preference profiles...")
    
    users_collection = db['users']
    users = list(users_collection.find())
    
    # Define preference archetypes
    preference_archetypes = [
        {
            'name': 'spicy_lover',
            'preferences': {'spicy': 0.9, 'mild': 0.2, 'sweet': 0.4},
            'cuisines': ['Malay', 'Indian', 'Thai'],
            'persona_preference': ['malay', 'indian']
        },
        {
            'name': 'health_conscious',
            'preferences': {'healthy': 0.9, 'fried': 0.2, 'vegetarian': 0.8},
            'cuisines': ['International', 'Chinese', 'Indian'],
            'persona_preference': ['chinese', 'indian']
        },
        {
            'name': 'traditional_food_lover',
            'preferences': {'traditional': 0.9, 'fusion': 0.3, 'authentic': 0.9},
            'cuisines': ['Malay', 'Chinese', 'Indian', 'Peranakan'],
            'persona_preference': ['malay', 'chinese', 'indian']
        },
        {
            'name': 'adventurous_eater',
            'preferences': {'fusion': 0.8, 'international': 0.9, 'experimental': 0.8},
            'cuisines': ['International', 'Fusion', 'Western'],
            'persona_preference': ['chinese', 'malay']
        },
        {
            'name': 'comfort_food_seeker',
            'preferences': {'comfort': 0.9, 'simple': 0.8, 'familiar': 0.9},
            'cuisines': ['Malay', 'Chinese', 'International'],
            'persona_preference': ['malay', 'chinese']
        }
    ]
    
    # Assign preference profiles to users
    user_profiles = {}
    for user in users:
        user_id = str(user['_id'])
        
        # Assign archetype (with some users having mixed preferences)
        if random.random() < 0.8:  # 80% get single archetype
            archetype = random.choice(preference_archetypes)
        else:  # 20% get mixed preferences
            arch1, arch2 = random.sample(preference_archetypes, 2)
            archetype = {
                'name': f"{arch1['name']}_mixed",
                'preferences': {**arch1['preferences'], **arch2['preferences']},
                'cuisines': list(set(arch1['cuisines'] + arch2['cuisines'])),
                'persona_preference': list(set(arch1['persona_preference'] + arch2['persona_preference']))
            }
        
        user_profiles[user_id] = archetype
    
    print(f"  👤 Created profiles for {len(user_profiles)} users")
    return user_profiles

def generate_reviews_for_recipe(db, recipe_data, category_name, user_profiles):
    """Generate reviews for a single recipe."""
    recipe_id = recipe_data['recipe_id']
    recipe = recipe_data['recipe']
    recipe_context = recipe_data['context']
    target_reviews = recipe_data['target_reviews']
    
    # Generate realistic ratings
    ratings = generate_realistic_ratings(category_name, target_reviews)
    
    # Get available users
    users_collection = db['users']
    available_users = list(users_collection.find({}, {'_id': 1}))
    
    if len(available_users) < target_reviews:
        print(f"  ⚠️  Not enough users for recipe {recipe_context['name']}")
        return []
    
    # Select users for this recipe (avoid same user reviewing twice)
    selected_users = random.sample(available_users, target_reviews)
    
    reviews = []
    for i, user in enumerate(selected_users):
        user_id = str(user['_id'])
        rating = ratings[i]
        
        # Select persona based on user profile and recipe
        user_profile = user_profiles.get(user_id, random.choice(list(user_profiles.values())))
        
        # Filter personas that match user preferences
        suitable_personas = [
            p for p in MALAYSIAN_PERSONAS 
            if any(pref in p['name_pattern'] for pref in user_profile['persona_preference'])
        ]
        
        if not suitable_personas:
            suitable_personas = MALAYSIAN_PERSONAS
        
        persona = random.choice(suitable_personas)
        
        # Generate review content
        review_text = generate_review_content(rating, recipe_context, persona)
        
        # Generate realistic timestamp (distributed over past 2 months)
        days_ago = random.randint(1, 60)  # 1-60 days ago
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        created_at = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
        
        review = {
            'recipe_id': recipe_id,
            'user_id': user_id,
            'rating': rating,
            'review_text': review_text,
            'created_at': created_at,
            'updated_at': created_at,
            'helpful_count': random.randint(0, max(1, rating * 2)),  # Higher rated reviews get more helpful votes
            'verified_purchase': random.choice([True, False]),
            'cuisine_context': recipe_context['cuisine'],
            'difficulty_rating': random.randint(max(1, rating-1), min(5, rating+1))  # Difficulty rating correlates with overall rating
        }
        
        reviews.append(review)
    
    return reviews

def optimize_recommendation_data(db, analysis_data):
    """Main optimization function."""
    print("\n🚀 Starting recommendation data optimization...")
    
    # Load recipe data from analysis
    phase1_recipes = analysis_data['strategy']['phase_1_priority_recipes']
    phase2_recipes = analysis_data['strategy']['phase_2_standard_recipes']
    all_recipes = phase1_recipes + phase2_recipes
    
    print(f"📊 Processing {len(all_recipes)} recipes")
    
    # Categorize recipes by quality
    recipe_categories = categorize_recipes_by_quality(db, all_recipes)
    
    # Create user preference profiles
    user_profiles = create_user_preference_profiles(db)
    
    # Generate reviews for each category
    all_generated_reviews = []
    
    for category_name, recipes in recipe_categories.items():
        print(f"\n📝 Generating reviews for {category_name}...")
        
        for recipe_data in recipes:
            reviews = generate_reviews_for_recipe(db, recipe_data, category_name, user_profiles)
            all_generated_reviews.extend(reviews)
            
            if len(reviews) > 0:
                print(f"  ✅ Generated {len(reviews)} reviews for '{recipe_data['context']['name']}'")
    
    return all_generated_reviews

def insert_generated_reviews(db, reviews):
    """Insert generated reviews into database."""
    print(f"\n💾 Inserting {len(reviews)} reviews into database...")
    
    if not reviews:
        print("  ⚠️  No reviews to insert")
        return
    
    reviews_collection = db['recipe_reviews']
    
    # Insert in batches to avoid overwhelming the database
    batch_size = 100
    inserted_count = 0
    
    for i in range(0, len(reviews), batch_size):
        batch = reviews[i:i + batch_size]
        try:
            result = reviews_collection.insert_many(batch)
            inserted_count += len(result.inserted_ids)
            print(f"  📝 Inserted batch {i//batch_size + 1}: {len(result.inserted_ids)} reviews")
        except Exception as e:
            print(f"  ❌ Error inserting batch {i//batch_size + 1}: {e}")
    
    print(f"✅ Successfully inserted {inserted_count} reviews")
    return inserted_count

def main():
    """Main optimization function."""
    print("🚀 SisaRasa Recommendation Engine Data Optimization")
    print("=" * 60)
    print(f"Optimization started at: {datetime.now().isoformat()}")
    
    # Connect to database
    db, client = connect_to_database()
    if db is None:
        return
    
    # Load analysis data
    analysis_data = load_analysis_data()
    if analysis_data is None:
        return
    
    try:
        # Create backups
        backup_collections(db)
        
        # Optimize recommendation data
        generated_reviews = optimize_recommendation_data(db, analysis_data)
        
        # Insert reviews into database
        inserted_count = insert_generated_reviews(db, generated_reviews)
        
        # Summary
        print(f"\n📋 OPTIMIZATION SUMMARY")
        print("=" * 40)
        print(f"Reviews generated: {len(generated_reviews)}")
        print(f"Reviews inserted: {inserted_count}")
        print(f"Target recipes processed: {len(analysis_data['strategy']['phase_1_priority_recipes']) + len(analysis_data['strategy']['phase_2_standard_recipes'])}")
        print(f"Optimization completed at: {datetime.now().isoformat()}")
        
    except Exception as e:
        print(f"❌ Error during optimization: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()
