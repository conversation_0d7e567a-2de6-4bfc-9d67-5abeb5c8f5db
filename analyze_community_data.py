#!/usr/bin/env python3
"""
Script to analyze and clean community feed data.
"""

from pymongo import MongoClient
from datetime import datetime
import json

def analyze_community_data():
    """Analyze the current community feed data."""
    client = MongoClient('mongodb://localhost:27017/')
    db = client['sisarasa']
    
    print("=== COMMUNITY FEED DATA ANALYSIS ===")
    
    # Check posts collection
    posts_collection = db['community_posts']
    total_posts = posts_collection.count_documents({})
    print(f"\nTotal community posts: {total_posts}")
    
    if total_posts > 0:
        print("\n--- Sample Posts ---")
        sample_posts = list(posts_collection.find({}).limit(5))
        for i, post in enumerate(sample_posts, 1):
            print(f"\nPost {i}:")
            print(f"  ID: {post.get('_id')}")
            print(f"  Content: {post.get('content', 'No content')[:100]}...")
            print(f"  User ID: {post.get('user_id', 'No user_id')}")
            print(f"  Username: {post.get('username', 'No username')}")
            print(f"  Created: {post.get('created_at', 'No date')}")
            print(f"  Recipe ID: {post.get('recipe_id', 'No recipe_id')}")
            print(f"  Type: {post.get('type', 'No type')}")
    
    # Check for test patterns
    print("\n--- Checking for Test Data ---")
    test_patterns = [
        'test', 'Test', 'TEST', 'debug', 'Debug', 'sample', 'Sample',
        'lorem ipsum', 'Lorem Ipsum', 'placeholder', 'Placeholder'
    ]
    
    test_posts = []
    for pattern in test_patterns:
        count = posts_collection.count_documents({
            '$or': [
                {'content': {'$regex': pattern, '$options': 'i'}},
                {'username': {'$regex': pattern, '$options': 'i'}}
            ]
        })
        if count > 0:
            print(f"  Found {count} posts matching '{pattern}'")
            posts = list(posts_collection.find({
                '$or': [
                    {'content': {'$regex': pattern, '$options': 'i'}},
                    {'username': {'$regex': pattern, '$options': 'i'}}
                ]
            }, {'_id': 1, 'content': 1, 'username': 1}))
            test_posts.extend(posts)
    
    # Check for duplicate posts
    print("\n--- Checking for Duplicates ---")
    pipeline = [
        {
            '$group': {
                '_id': {
                    'content': '$content',
                    'user_id': '$user_id'
                },
                'count': {'$sum': 1},
                'posts': {'$push': '$_id'}
            }
        },
        {
            '$match': {
                'count': {'$gt': 1}
            }
        }
    ]
    
    duplicates = list(posts_collection.aggregate(pipeline))
    if duplicates:
        print(f"  Found {len(duplicates)} groups of duplicate posts")
        for dup in duplicates[:3]:  # Show first 3 groups
            print(f"    Content: {dup['_id']['content'][:50]}...")
            print(f"    Count: {dup['count']}")
    else:
        print("  No duplicate posts found")
    
    # Check for invalid data
    print("\n--- Checking for Invalid Data ---")
    
    # Posts without content
    no_content = posts_collection.count_documents({
        '$or': [
            {'content': {'$exists': False}},
            {'content': ''},
            {'content': None}
        ]
    })
    if no_content > 0:
        print(f"  Found {no_content} posts without content")
    
    # Posts without user_id
    no_user = posts_collection.count_documents({
        '$or': [
            {'user_id': {'$exists': False}},
            {'user_id': ''},
            {'user_id': None}
        ]
    })
    if no_user > 0:
        print(f"  Found {no_user} posts without user_id")
    
    # Posts with invalid dates
    invalid_dates = posts_collection.count_documents({
        '$or': [
            {'created_at': {'$exists': False}},
            {'created_at': None}
        ]
    })
    if invalid_dates > 0:
        print(f"  Found {invalid_dates} posts with invalid dates")
    
    return {
        'total_posts': total_posts,
        'test_posts': test_posts,
        'duplicates': duplicates,
        'no_content': no_content,
        'no_user': no_user,
        'invalid_dates': invalid_dates
    }

def clean_community_data():
    """Clean the community feed data."""
    client = MongoClient('mongodb://localhost:27017/')
    db = client['sisarasa']
    posts_collection = db['community_posts']
    
    print("\n=== CLEANING COMMUNITY FEED DATA ===")
    
    # Ask for confirmation
    response = input('\nDo you want to clean the community feed data? (yes/no): ')
    
    if response.lower() == 'yes':
        print('\nCleaning community feed data...')
        
        # 1. Remove test posts
        test_patterns = [
            'test', 'Test', 'TEST', 'debug', 'Debug', 'sample', 'Sample',
            'lorem ipsum', 'Lorem Ipsum', 'placeholder', 'Placeholder'
        ]
        
        for pattern in test_patterns:
            result = posts_collection.delete_many({
                '$or': [
                    {'content': {'$regex': pattern, '$options': 'i'}},
                    {'username': {'$regex': pattern, '$options': 'i'}}
                ]
            })
            if result.deleted_count > 0:
                print(f'  - Deleted {result.deleted_count} posts matching "{pattern}"')
        
        # 2. Remove posts without content
        result = posts_collection.delete_many({
            '$or': [
                {'content': {'$exists': False}},
                {'content': ''},
                {'content': None}
            ]
        })
        if result.deleted_count > 0:
            print(f'  - Deleted {result.deleted_count} posts without content')
        
        # 3. Remove posts without user_id
        result = posts_collection.delete_many({
            '$or': [
                {'user_id': {'$exists': False}},
                {'user_id': ''},
                {'user_id': None}
            ]
        })
        if result.deleted_count > 0:
            print(f'  - Deleted {result.deleted_count} posts without user_id')
        
        # 4. Handle duplicates (keep the most recent one)
        pipeline = [
            {
                '$group': {
                    '_id': {
                        'content': '$content',
                        'user_id': '$user_id'
                    },
                    'count': {'$sum': 1},
                    'posts': {'$push': {'id': '$_id', 'created_at': '$created_at'}}
                }
            },
            {
                '$match': {
                    'count': {'$gt': 1}
                }
            }
        ]
        
        duplicates = list(posts_collection.aggregate(pipeline))
        deleted_duplicates = 0
        
        for dup_group in duplicates:
            posts = dup_group['posts']
            # Sort by created_at and keep the most recent
            posts.sort(key=lambda x: x.get('created_at', datetime.min), reverse=True)
            # Delete all but the first (most recent)
            for post in posts[1:]:
                posts_collection.delete_one({'_id': post['id']})
                deleted_duplicates += 1
        
        if deleted_duplicates > 0:
            print(f'  - Deleted {deleted_duplicates} duplicate posts')
        
        # 5. Clean up related data (likes, comments)
        print('\nCleaning up related data...')
        
        # Get all remaining post IDs
        remaining_post_ids = set()
        for post in posts_collection.find({}, {'_id': 1}):
            remaining_post_ids.add(str(post['_id']))
        
        # Clean up orphaned likes and comments
        for collection_name in ['post_likes', 'post_comments']:
            if collection_name in db.list_collection_names():
                collection = db[collection_name]
                orphaned = []
                for record in collection.find({}):
                    post_id = record.get('post_id', '')
                    if post_id not in remaining_post_ids:
                        orphaned.append(record['_id'])
                
                if orphaned:
                    result = collection.delete_many({'_id': {'$in': orphaned}})
                    print(f'  - Deleted {result.deleted_count} orphaned records from {collection_name}')
        
        print('\nCommunity feed cleanup completed!')
        
        # Show final stats
        final_count = posts_collection.count_documents({})
        print(f'Final post count: {final_count}')
        
    else:
        print('Cleanup cancelled.')

def main():
    analysis = analyze_community_data()
    
    if analysis['total_posts'] > 0:
        clean_community_data()
    else:
        print('\nNo posts found to clean.')

if __name__ == '__main__':
    main()
