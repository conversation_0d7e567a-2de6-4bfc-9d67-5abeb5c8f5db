{"verification_date": "2025-07-27T16:37:28.370984", "optimization_summary": {"overall_score": 93.17193552417844, "component_scores": {"review_distribution": 100, "cultural_authenticity": 83.00220750551877, "rating_realism": 89.68553459119498, "cold_start_solution": 100}, "weights": {"review_distribution": 0.3, "cultural_authenticity": 0.25, "rating_realism": 0.25, "cold_start_solution": 0.2}, "grade": "A+", "success_level": "Exceptional - All optimization goals exceeded"}, "detailed_metrics": {"review_distribution": {"total_recipes": 884, "recipes_analyzed": 1216, "review_count_distribution": {"3": 878, "4": 6}, "recipes_with_target_reviews": 884, "recipes_with_some_reviews": 0, "recipes_with_no_reviews": 0, "target_achievement_rate": 100.0, "coverage_rate": 100.0}, "cultural_authenticity": {"total_reviews": 3171, "reviews_with_malaysian_terms": 2632, "term_usage_breakdown": {"food_terms": 1140, "expressions": 1322, "casual_terms": 2364, "negative_terms": 832}, "cuisine_cultural_alignment": {"Unknown": 18, "Chinese": 4, "Indonesian": 12, "International": 2572, "Test Cuisine": 13, "": 6, "American": 4, "Italian": 3}, "authenticity_score": 83.00220750551877}, "rating_realism": {"total_ratings": 3180, "rating_distribution": {"1": 197, "4": 1055, "5": 718, "3": 843, "2": 367}, "average_rating": 3.5440251572327046, "rating_percentages": {"1": 6.19496855345912, "2": 11.540880503144654, "3": 26.50943396226415, "4": 33.17610062893082, "5": 22.57861635220126}, "realism_score": 89.68553459119498, "recipe_average_distribution": [4.25, 5, 3.5, 3, 2.6666666666666665, 3, 4.5, 4, 3.5, 4.5, 3.3333333333333335, 4.5, 3, 4.5, 3, 3, 4, 4.6, 4.5, 4.333333333333333, 5, 3.5, 4.333333333333333, 4.5, 5, 4, 3, 3.6666666666666665, 4, 4, 4, 3.5, 4.5, 5, 4, 4, 4, 4.5, 3.3333333333333335, 4, 3, 3, 4, 2.5, 4.5, 3.3333333333333335, 5, 4.5, 5, 2.75, 4.8, 4.5, 4, 4.5, 3.3333333333333335, 2, 2.5, 4, 3.5, 4, 5, 4, 5, 4.5, 2.5, 4, 4.5, 3.5, 3.5, 4.333333333333333, 5, 4, 4.4, 4.333333333333333, 3, 5, 4.5, 4.5, 2.5, 2.5, 2, 3.6666666666666665, 3.5, 3.6666666666666665, 4, 4.333333333333333, 4, 3, 3.5, 4, 4.5, 1.5, 4, 5, 2.6666666666666665, 3, 3.8, 4, 4.5, 3.5, 2, 4, 5, 4.333333333333333, 4, 3.3333333333333335, 3.6666666666666665, 2.5, 4.5, 4, 3.3333333333333335, 3.5, 4, 4, 3, 4.5, 3.5, 5, 3.5, 2.5, 2.6666666666666665, 3, 3, 4.5, 3, 2.5, 5, 3.5, 4.5, 4.5, 5, 3.6666666666666665, 4.5, 3, 2.5, 4.5, 3, 3.5, 4.666666666666667, 3.5, 4.75, 3.3333333333333335, 4, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 5, 4, 4, 4, 3, 3.3333333333333335, 4.666666666666667, 5, 3.6666666666666665, 4.666666666666667, 4, 2.6666666666666665, 4.333333333333333, 4.666666666666667, 4.333333333333333, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 4, 4, 4.666666666666667, 4.333333333333333, 5, 4, 4, 3, 5, 3.6666666666666665, 4.333333333333333, 4.333333333333333, 4.333333333333333, 4, 2.6666666666666665, 4, 4, 4.666666666666667, 3.3333333333333335, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 4, 3.3333333333333335, 3.6666666666666665, 2.6666666666666665, 4.666666666666667, 3.3333333333333335, 4, 4, 4.666666666666667, 2.6666666666666665, 4.333333333333333, 3, 4, 4.333333333333333, 3, 3, 4.333333333333333, 3.3333333333333335, 4, 4.333333333333333, 5, 4.333333333333333, 5, 4.333333333333333, 5, 4.666666666666667, 4.333333333333333, 4.666666666666667, 4, 3, 4.333333333333333, 5, 3.6666666666666665, 3.3333333333333335, 3.3333333333333335, 3.6666666666666665, 4.666666666666667, 3.6666666666666665, 3, 3.3333333333333335, 3.6666666666666665, 4.666666666666667, 3.6666666666666665, 3.6666666666666665, 4, 3, 4.333333333333333, 3.6666666666666665, 4, 4.666666666666667, 4, 3.6666666666666665, 4.666666666666667, 4.333333333333333, 3.6666666666666665, 3, 3, 3.6666666666666665, 3.3333333333333335, 5, 3.3333333333333335, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 4.666666666666667, 3.6666666666666665, 4.333333333333333, 4.333333333333333, 4.666666666666667, 4, 4, 4, 4, 3.6666666666666665, 4, 2, 4.333333333333333, 4, 3.6666666666666665, 4.666666666666667, 4, 3.3333333333333335, 4, 4.333333333333333, 4, 4.666666666666667, 4.333333333333333, 4, 3.6666666666666665, 4.333333333333333, 4, 3.6666666666666665, 5, 3.3333333333333335, 3.3333333333333335, 4, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 4, 4, 3.3333333333333335, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 4.333333333333333, 4.666666666666667, 4.666666666666667, 4.666666666666667, 3.6666666666666665, 4.666666666666667, 2.6666666666666665, 5, 4.333333333333333, 3.3333333333333335, 2.3333333333333335, 4, 4, 4, 4, 4, 3.3333333333333335, 4.333333333333333, 2.6666666666666665, 3.6666666666666665, 3, 3.3333333333333335, 4, 3.6666666666666665, 2.6666666666666665, 3.6666666666666665, 3.3333333333333335, 4, 3.6666666666666665, 2.3333333333333335, 4, 4, 3, 4, 4, 4.333333333333333, 2.6666666666666665, 4, 4.666666666666667, 2.3333333333333335, 3.6666666666666665, 4.333333333333333, 3, 2.6666666666666665, 3.3333333333333335, 3.3333333333333335, 4, 3.3333333333333335, 3, 4.333333333333333, 3, 3.3333333333333335, 3.6666666666666665, 4, 3.3333333333333335, 4.333333333333333, 4.333333333333333, 4, 3.6666666666666665, 3.6666666666666665, 2.6666666666666665, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 4.333333333333333, 3.6666666666666665, 3, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 4, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 4.333333333333333, 4, 3.6666666666666665, 3, 4, 3.6666666666666665, 4, 3.3333333333333335, 4.666666666666667, 3.6666666666666665, 4, 3.3333333333333335, 4, 3.3333333333333335, 4, 3.3333333333333335, 4, 3, 4.333333333333333, 3.3333333333333335, 3.3333333333333335, 5, 3.6666666666666665, 4.333333333333333, 4.333333333333333, 3.3333333333333335, 2.6666666666666665, 2.3333333333333335, 3.3333333333333335, 3, 4.666666666666667, 3, 3, 4.666666666666667, 3, 3.6666666666666665, 4.333333333333333, 4, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 4.333333333333333, 3.6666666666666665, 4.333333333333333, 3.3333333333333335, 3, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 4, 4.666666666666667, 3.6666666666666665, 3.3333333333333335, 2.6666666666666665, 2, 4, 2.6666666666666665, 4.333333333333333, 4.333333333333333, 4.333333333333333, 3.3333333333333335, 4, 4.666666666666667, 3.6666666666666665, 3.6666666666666665, 4, 3.6666666666666665, 4.333333333333333, 2.3333333333333335, 2.3333333333333335, 2.6666666666666665, 4, 2.6666666666666665, 3, 3, 4, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 4.666666666666667, 1.3333333333333333, 2.6666666666666665, 3.6666666666666665, 2.6666666666666665, 4.666666666666667, 4.333333333333333, 3.6666666666666665, 3, 2, 3.6666666666666665, 3.6666666666666665, 3.3333333333333335, 4, 3, 4.333333333333333, 4, 4, 2.6666666666666665, 2, 4, 4.333333333333333, 3, 3, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3, 4.333333333333333, 2.6666666666666665, 4.333333333333333, 4, 4, 4, 3.3333333333333335, 3.3333333333333335, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 3, 4.666666666666667, 3.3333333333333335, 3, 3.3333333333333335, 4, 4.333333333333333, 3.3333333333333335, 5, 4, 2.6666666666666665, 3, 3.6666666666666665, 3.6666666666666665, 4, 3.3333333333333335, 3.6666666666666665, 4.333333333333333, 3.6666666666666665, 3.3333333333333335, 2.3333333333333335, 2.6666666666666665, 3.6666666666666665, 3, 4, 4, 3, 2.6666666666666665, 2, 4, 3.6666666666666665, 3.6666666666666665, 4.666666666666667, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 3.3333333333333335, 3, 4.333333333333333, 4.666666666666667, 4, 4.333333333333333, 3.6666666666666665, 2.6666666666666665, 2.3333333333333335, 2.6666666666666665, 2.3333333333333335, 3.6666666666666665, 4, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 3.3333333333333335, 4, 3.3333333333333335, 4, 3, 3, 3.6666666666666665, 3.6666666666666665, 3.3333333333333335, 2.6666666666666665, 4.333333333333333, 3, 3, 1.3333333333333333, 3.6666666666666665, 4.666666666666667, 4, 4, 4, 3.3333333333333335, 3.3333333333333335, 4, 3.3333333333333335, 3.3333333333333335, 4.333333333333333, 3.3333333333333335, 4, 3.6666666666666665, 4.666666666666667, 3.3333333333333335, 3.3333333333333335, 2.6666666666666665, 3.3333333333333335, 2.6666666666666665, 4.333333333333333, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 2, 3, 3, 4.333333333333333, 3.6666666666666665, 4.333333333333333, 4.666666666666667, 4, 3.6666666666666665, 4.333333333333333, 4, 2.3333333333333335, 4.333333333333333, 4.333333333333333, 4.333333333333333, 3.3333333333333335, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 3, 3.3333333333333335, 3.3333333333333335, 4.333333333333333, 4.333333333333333, 4, 3.3333333333333335, 3.6666666666666665, 3, 4.666666666666667, 2.3333333333333335, 4, 3.3333333333333335, 3.3333333333333335, 3.3333333333333335, 4.333333333333333, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 4.666666666666667, 4.666666666666667, 4.666666666666667, 4.333333333333333, 4.666666666666667, 3.6666666666666665, 2.6666666666666665, 2.6666666666666665, 4.333333333333333, 4, 3, 4, 3.6666666666666665, 4, 3.6666666666666665, 2.6666666666666665, 3, 3.6666666666666665, 4.333333333333333, 2.6666666666666665, 3, 4, 3.3333333333333335, 2, 3, 2.6666666666666665, 3, 3.75, 2.6666666666666665, 3.3333333333333335, 1.6666666666666667, 4, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 4, 3.3333333333333335, 4.666666666666667, 3.6666666666666665, 3, 3.6666666666666665, 3.3333333333333335, 4.666666666666667, 4.666666666666667, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 3, 3.3333333333333335, 3.6666666666666665, 3, 4.333333333333333, 3.3333333333333335, 4, 1.6666666666666667, 4, 4.666666666666667, 3.6666666666666665, 4, 3.6666666666666665, 3, 4, 3.6666666666666665, 4, 4, 4, 3.6666666666666665, 3.6666666666666665, 2.6666666666666665, 3.6666666666666665, 3.3333333333333335, 2.3333333333333335, 3.6666666666666665, 4, 3, 3, 4, 3, 2.6666666666666665, 3.6666666666666665, 3.3333333333333335, 4, 4, 2.6666666666666665, 4, 4.333333333333333, 4.333333333333333, 4.333333333333333, 4, 3.6666666666666665, 3.3333333333333335, 3, 3.6666666666666665, 3.6666666666666665, 4.666666666666667, 4.333333333333333, 3, 3.6666666666666665, 3.3333333333333335, 4, 2.6666666666666665, 3.3333333333333335, 3, 3, 3.6666666666666665, 4.333333333333333, 4, 3.6666666666666665, 4, 3.6666666666666665, 3.3333333333333335, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 3, 2.3333333333333335, 4, 4.333333333333333, 4, 4.666666666666667, 3, 3.6666666666666665, 3.6666666666666665, 4.333333333333333, 3.3333333333333335, 4.333333333333333, 3.6666666666666665, 3.6666666666666665, 4, 4, 4.333333333333333, 3, 4, 4.333333333333333, 4, 2, 2.6666666666666665, 3.3333333333333335, 2, 3.6666666666666665, 2.6666666666666665, 3, 3.3333333333333335, 2.3333333333333335, 3.3333333333333335, 2, 3.3333333333333335, 3, 3, 2.6666666666666665, 3, 2.3333333333333335, 3.3333333333333335, 3.6666666666666665, 3, 3.3333333333333335, 3, 2.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 3.6666666666666665, 3, 2.6666666666666665, 2, 3, 3.6666666666666665, 4, 2.6666666666666665, 3, 3, 3, 3.3333333333333335, 3.6666666666666665, 4.666666666666667, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 2.6666666666666665, 3, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 3.3333333333333335, 3, 2.3333333333333335, 3.3333333333333335, 2.6666666666666665, 2.6666666666666665, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 2.3333333333333335, 3, 4, 3.3333333333333335, 3.3333333333333335, 1.6666666666666667, 2, 2.6666666666666665, 4.333333333333333, 2.3333333333333335, 2.3333333333333335, 2.6666666666666665, 3, 2.3333333333333335, 3, 3.6666666666666665, 3, 3.6666666666666665, 3.3333333333333335, 3.6666666666666665, 4, 3.3333333333333335, 3.3333333333333335, 4.333333333333333, 3, 2.3333333333333335, 2.6666666666666665, 4, 2.3333333333333335, 3.3333333333333335, 4, 3.6666666666666665, 2.3333333333333335, 4, 4, 2.3333333333333335, 3, 3, 2.3333333333333335, 2.6666666666666665, 3, 3, 3, 3, 4, 3.3333333333333335, 2.3333333333333335, 2.6666666666666665, 3.6666666666666665, 3.3333333333333335, 3, 3.3333333333333335, 3.3333333333333335, 3.3333333333333335, 2, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 2, 3.3333333333333335, 3, 3, 3.6666666666666665, 2.3333333333333335, 3, 3.6666666666666665, 3, 4, 2.3333333333333335, 2.6666666666666665, 3.6666666666666665, 2.3333333333333335, 3.6666666666666665, 3, 2, 2.6666666666666665, 2.3333333333333335, 2, 3.6666666666666665, 3.3333333333333335, 3.3333333333333335, 3.3333333333333335, 3.3333333333333335, 2.3333333333333335, 4.333333333333333, 3.3333333333333335, 3.6666666666666665, 2, 3, 4.333333333333333, 3.6666666666666665, 3, 2, 2.6666666666666665, 2.3333333333333335, 4.333333333333333, 3.3333333333333335, 3.6666666666666665, 3.6666666666666665, 2.6666666666666665, 4.666666666666667, 3, 3.6666666666666665, 3, 3.6666666666666665, 3, 2.6666666666666665, 3.3333333333333335, 3.3333333333333335, 2.3333333333333335, 4.666666666666667, 2.3333333333333335, 2, 3.3333333333333335, 2.3333333333333335, 3.3333333333333335, 3, 3, 4, 2.3333333333333335, 3, 3.3333333333333335, 3.3333333333333335, 3.6666666666666665, 2.6666666666666665, 3, 2.6666666666666665, 3, 3.6666666666666665, 3, 4, 3, 2.6666666666666665, 2.3333333333333335, 3.3333333333333335, 3.6666666666666665, 3.3333333333333335, 3, 1.6666666666666667, 2.75, 3.6666666666666665, 4, 4, 3, 3, 2.6666666666666665, 4, 3.3333333333333335, 3.3333333333333335, 2.6666666666666665, 2.6666666666666665, 3.3333333333333335, 3.6666666666666665, 2, 3.3333333333333335, 2.6666666666666665, 3, 4, 3.3333333333333335, 2.6666666666666665, 3, 2.6666666666666665, 3.3333333333333335, 3.6666666666666665, 2.3333333333333335, 2.3333333333333335, 3, 2.3333333333333335, 3, 1.3333333333333333, 2.3333333333333335, 3, 4, 3, 2.6666666666666665, 2, 2, 1.6666666666666667, 2, 2, 2.3333333333333335, 3, 2.3333333333333335, 1.6666666666666667, 2, 2.6666666666666665, 2.3333333333333335, 2.3333333333333335, 2.6666666666666665, 1.6666666666666667, 2, 2, 2.3333333333333335, 2.3333333333333335, 2.6666666666666665, 3, 2, 2, 2.6666666666666665, 1.6666666666666667, 3.3333333333333335, 4, 1, 2.6666666666666665, 3.3333333333333335, 2.6666666666666665, 2.3333333333333335, 2.3333333333333335, 3]}, "cold_start_solutions": {"total_recipes": 884, "cold_start_recipes": 0, "well_covered_recipes": 884, "cold_start_percentage": 0.0, "coverage_success_rate": 100.0, "total_active_users": 177, "bridge_users": 33, "bridge_user_percentage": 18.64406779661017}}, "recommendations": ["Excellent optimization achieved! Monitor and maintain quality."]}