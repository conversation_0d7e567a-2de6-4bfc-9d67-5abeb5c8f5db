#!/usr/bin/env python3
"""
Test script to verify profile image functionality
"""

import requests
import json
import base64

BASE_URL = "http://localhost:5000"

def create_test_user_with_image():
    """Create a test user and upload a profile image"""
    print("Creating test user...")
    
    # Register a new user
    register_data = {
        "name": "Test User Profile",
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/signup", json=register_data)
        print(f"Registration status: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ User registered successfully")
            
            # Login to get token
            login_data = {
                "email": "<EMAIL>",
                "password": "testpassword123"
            }
            
            login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
            if login_response.status_code == 200:
                login_result = login_response.json()
                token = login_result.get('token')
                print("✅ Login successful")
                
                # Create a simple test image (1x1 pixel PNG)
                test_image_data = base64.b64decode(
                    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGAWA0drAAAAABJRU5ErkJggg=='
                )
                
                # Upload profile image
                files = {'image': ('test.png', test_image_data, 'image/png')}
                headers = {'Authorization': f'Bearer {token}'}
                
                upload_response = requests.post(f"{BASE_URL}/api/auth/upload-profile-image", 
                                              files=files, headers=headers)
                print(f"Upload status: {upload_response.status_code}")
                
                if upload_response.status_code == 200:
                    upload_result = upload_response.json()
                    print("✅ Profile image uploaded successfully")
                    print(f"Profile image data: {upload_result.get('profile_image', 'N/A')[:50]}...")
                    return token
                else:
                    print(f"❌ Upload failed: {upload_response.text}")
            else:
                print(f"❌ Login failed: {login_response.text}")
        else:
            print(f"❌ Registration failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    return None

def test_auth_me_endpoint(token):
    """Test the /api/auth/me endpoint"""
    if not token:
        print("❌ No token available")
        return
        
    print("\nTesting /api/auth/me endpoint...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response data:")
            print(json.dumps(data, indent=2))
            
            if 'user' in data and 'profile_image' in data['user']:
                profile_image = data['user']['profile_image']
                if profile_image:
                    print(f"✅ Profile image found in /me endpoint: {profile_image[:50]}...")
                else:
                    print("⚠️ Profile image field exists but is null")
            else:
                print("❌ Profile image field missing from /me endpoint")
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_profile_image_endpoint(token):
    """Test the dedicated profile image endpoint"""
    if not token:
        print("❌ No token available")
        return
        
    print("\nTesting /api/auth/profile-image/current endpoint...")
    
    try:
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(f"{BASE_URL}/api/auth/profile-image/current", headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response data:")
            print(json.dumps(data, indent=2))
            
            if 'profile_image' in data:
                profile_image = data['profile_image']
                if profile_image:
                    print(f"✅ Profile image found in dedicated endpoint: {profile_image[:50]}...")
                else:
                    print("⚠️ Profile image field exists but is null")
        elif response.status_code == 404:
            print("⚠️ No profile image found (404)")
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Profile Image Functionality")
    print("=" * 50)
    
    # Create test user with profile image
    token = create_test_user_with_image()
    
    # Test both endpoints
    test_auth_me_endpoint(token)
    test_profile_image_endpoint(token)
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")
    print("\nNow you can test the community page with the user:")
    print("Email: <EMAIL>")
    print("Password: testpassword123")
